// 成长日记功能测试文件
// 这个文件用于测试成长日记的核心功能

// 模拟微信小程序环境
const mockWx = {
  getStorageSync: function(key) {
    const storage = this._storage || {}
    return storage[key] || null
  },
  setStorageSync: function(key, data) {
    this._storage = this._storage || {}
    this._storage[key] = data
  },
  _storage: {}
}

// 模拟 getApp 函数
global.getApp = function() {
  return {
    globalData: {
      policyConfig: {
        monthlyAmount: 300,
        maxAge: 3,
        policyStartDate: '2025-01-01',
        minBirthDate: '1990-01-01'
      }
    }
  }
}

// 模拟 wx 对象
global.wx = mockWx

// 引入成长日记模块
const growthDiary = require('./utils/growthDiary')

// 测试函数
function runTests() {
  console.log('开始测试成长日记功能...\n')
  
  // 测试1: 创建新的成长日记
  console.log('测试1: 创建新的成长日记')
  const childId = 'test_child_1'
  const year = 2025
  const month = 1
  
  const newDiary = growthDiary.createGrowthDiary(childId, year, month)
  console.log('✓ 成功创建新日记，ID:', newDiary.id)
  console.log('✓ 日记年月:', newDiary.year, '年', newDiary.month, '月')
  
  // 测试2: 验证数据模型
  console.log('\n测试2: 验证数据模型')
  const validation = growthDiary.validateGrowthDiary(newDiary)
  if (validation.valid) {
    console.log('✓ 数据模型验证通过')
  } else {
    console.log('✗ 数据模型验证失败:', validation.errors)
  }
  
  // 测试3: 保存日记
  console.log('\n测试3: 保存日记')
  // 添加一些测试数据
  newDiary.careRequirements.skinCare = '每天洗澡后涂抹润肤霜'
  newDiary.feeding.breastfeeding.frequency = 8
  newDiary.feeding.breastfeeding.duration = 15
  newDiary.urination.frequency = 12
  newDiary.defecation.frequency = 3
  newDiary.parentNotes.observations = '宝宝这个月开始会翻身了'
  
  const saveResult = growthDiary.saveGrowthDiary(newDiary)
  if (saveResult) {
    console.log('✓ 成功保存日记')
  } else {
    console.log('✗ 保存日记失败')
  }
  
  // 测试4: 获取日记
  console.log('\n测试4: 获取日记')
  const retrievedDiary = growthDiary.getGrowthDiary(childId, year, month)
  if (retrievedDiary) {
    console.log('✓ 成功获取日记')
    console.log('✓ 皮肤护理:', retrievedDiary.careRequirements.skinCare)
    console.log('✓ 母乳喂养次数:', retrievedDiary.feeding.breastfeeding.frequency)
    console.log('✓ 家长观察:', retrievedDiary.parentNotes.observations)
  } else {
    console.log('✗ 获取日记失败')
  }
  
  // 测试5: 获取日记列表
  console.log('\n测试5: 获取日记列表')
  const diaries = growthDiary.getGrowthDiaries(childId)
  console.log('✓ 获取到', diaries.length, '条日记记录')
  
  // 测试6: 创建第二个月的日记
  console.log('\n测试6: 创建第二个月的日记')
  const secondDiary = growthDiary.createGrowthDiary(childId, year, month + 1)
  secondDiary.careRequirements.skinCare = '继续使用润肤霜，注意保湿'
  secondDiary.parentNotes.observations = '宝宝开始尝试坐立'
  
  const saveResult2 = growthDiary.saveGrowthDiary(secondDiary)
  if (saveResult2) {
    console.log('✓ 成功保存第二个月日记')
  }
  
  // 测试7: 再次获取日记列表
  console.log('\n测试7: 再次获取日记列表')
  const updatedDiaries = growthDiary.getGrowthDiaries(childId)
  console.log('✓ 现在有', updatedDiaries.length, '条日记记录')
  updatedDiaries.forEach((diary, index) => {
    console.log(`  ${index + 1}. ${diary.year}年${diary.month}月`)
  })
  
  // 测试8: 删除日记
  console.log('\n测试8: 删除日记')
  const deleteResult = growthDiary.deleteGrowthDiary(childId, year, month + 1)
  if (deleteResult) {
    console.log('✓ 成功删除第二个月日记')
  }
  
  const finalDiaries = growthDiary.getGrowthDiaries(childId)
  console.log('✓ 删除后剩余', finalDiaries.length, '条日记记录')
  
  // 测试9: 格式化函数
  console.log('\n测试9: 格式化函数')
  const formattedMonth = growthDiary.formatMonth(year, month)
  console.log('✓ 格式化月份:', formattedMonth)
  
  const daysInMonth = growthDiary.getDaysInMonth(year, month)
  console.log('✓ 该月天数:', daysInMonth)
  
  console.log('\n所有测试完成！')
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = { runTests }
} else {
  // 浏览器环境
  runTests()
}

// 如果直接运行此文件
if (require.main === module) {
  runTests()
}
