/* diary-edit.wxss - 成长日记编辑页面样式 */

/* 引入全局样式变量 */
@import "../../app.wxss";

/* 头部样式 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24rpx;
}

.title-section {
  flex: 1;
}

.header-actions {
  display: flex;
  gap: 16rpx;
  flex-shrink: 0;
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-secondary {
  background: var(--bg-light);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

/* 表单内容 */
.form-content {
  margin-top: 32rpx;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 表单章节 */
.form-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid var(--border-light);
}

.section-title .icon {
  font-size: 32rpx;
}

.section-title text:last-child {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

/* 子章节 */
.subsection {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: var(--bg-light);
  border-radius: 12rpx;
  border: 1px solid var(--border-light);
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20rpx;
  display: block;
  padding-bottom: 12rpx;
  border-bottom: 1px solid var(--border-light);
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group.half {
  flex: 1;
}

.form-row {
  display: flex;
  gap: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 24rpx;
  border: 1px solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 1px solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background: white;
  box-sizing: border-box;
  resize: vertical;
}

.form-textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* 选择器样式 */
.picker-value {
  padding: 24rpx;
  border: 1px solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background: white;
  min-height: 28rpx;
  display: flex;
  align-items: center;
}

/* 开关样式 */
.switch-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1px solid var(--border-light);
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-save {
  width: 100%;
  padding: 24rpx;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.btn-save:disabled {
  background: var(--text-secondary);
  opacity: 0.6;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .form-row {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .form-group.half {
    flex: none;
  }
}
