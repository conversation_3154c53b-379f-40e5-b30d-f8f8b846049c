/* diary-detail.wxss - 成长日记详情页面样式 */

/* 引入全局样式变量 */
@import "../../app.wxss";

/* 页面容器 */
.page {
  background: #f7f8fa;
  min-height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid #f0f0f0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.navbar-left,
.navbar-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back,
.icon-more {
  font-size: 36rpx;
  color: #333;
  font-weight: 300;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 主内容区 */
.main-content {
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx));
  padding-bottom: 40rpx;
}

/* 日记卡片 */
.diary-card {
  margin: 32rpx 24rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
}

.date-badge {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 20rpx;
}

.date-icon {
  font-size: 24rpx;
}

.date-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

/* 卡片内容 */
.card-body {
  padding: 32rpx;
}

.diary-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 40rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.diary-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 32rpx;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.progress-label {
  font-size: 26rpx;
  color: #666;
}

.progress-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 卡片底部 */
.card-footer {
  padding: 0 32rpx 32rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-weight: 500;
}

.action-btn.edit {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}

.action-btn.share {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
}

.action-btn.edit .btn-icon,
.action-btn.edit .btn-text {
  color: white;
}

.action-btn.share .btn-icon,
.action-btn.share .btn-text {
  color: #666;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 26rpx;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.btn {
  padding: 18rpx 32rpx;
  border-radius: 40rpx;
  border: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  flex: 1;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(79, 70, 229, 0.4);
}

.btn-secondary {
  background: rgba(255,255,255,0.2);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.3);
}

/* 内容区域 */
.content {
  padding: 32rpx;
  margin-top: -32rpx;
  position: relative;
  z-index: 3;
}

/* 章节卡片 */
.section-card {
  background: white;
  border-radius: 40rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 64rpx rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.8);
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 32rpx 36rpx;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.section-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(79, 70, 229, 0.3);
}

.section-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
}

.toggle-icon {
  font-size: 24rpx;
  color: #64748b;
}

.section-content {
  padding: 36rpx;
  background: white;
}

/* 信息项 */
.info-grid {
  display: grid;
  gap: 32rpx;
}

.info-item {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 24rpx 28rpx;
  border-radius: 20rpx;
  border-left: 6rpx solid #4f46e5;
}

.info-label {
  font-size: 24rpx;
  color: #4f46e5;
  font-weight: 600;
  margin-bottom: 6rpx;
  display: block;
}

.info-value {
  font-size: 26rpx;
  color: #1e293b;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

/* 高亮卡片样式 */
.highlight-card {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.highlight-card .section-header {
  background: rgba(255,255,255,0.1);
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.highlight-card .section-name {
  color: white;
}

.highlight-card .info-item {
  background: rgba(255,255,255,0.1);
  border-left: 8rpx solid rgba(255,255,255,0.5);
}

.highlight-card .info-label {
  color: rgba(255,255,255,0.9);
}

.highlight-card .info-value {
  color: white;
}

/* 子章节 */
.subsection {
  margin-bottom: 36rpx;
  padding: 28rpx;
  background: linear-gradient(135deg, var(--bg-light) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 16rpx;
  border: 1px solid var(--border-light);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid var(--primary-color);
  position: relative;
}

.subsection-title::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  background: var(--primary-color);
  border-radius: 50%;
  margin-right: 12rpx;
}

/* 列表样式 */
.list-section {
  margin-bottom: 28rpx;
  padding: 20rpx;
  background: rgba(79, 70, 229, 0.02);
  border-radius: 12rpx;
  border-left: 4rpx solid var(--primary-color);
}

.list-section:last-child {
  margin-bottom: 0;
}

.list-title {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.list-item {
  margin-bottom: 12rpx;
  padding: 8rpx 0;
  display: flex;
  align-items: flex-start;
}

.list-item:last-child {
  margin-bottom: 0;
}

.list-item text {
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.7;
}

.list-item::before {
  content: '•';
  color: var(--primary-color);
  font-weight: bold;
  margin-right: 12rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

/* 加载和空状态 */
.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  background: white;
  border-radius: 20rpx;
  margin: 40rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.loading-icon, .empty-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
  display: block;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.empty-icon {
  opacity: 0.7;
}

.loading-text, .empty-text {
  font-size: 30rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20rpx;
    min-height: auto;
  }

  .main-title {
    font-size: 36rpx;
  }

  .diary-stats {
    flex-wrap: wrap;
    gap: 12rpx;
  }

  .header-actions {
    justify-content: flex-end;
    gap: 8rpx;
  }

  .btn-icon {
    width: 48rpx;
    height: 48rpx;
    font-size: 20rpx;
  }

  .btn {
    padding: 14rpx 24rpx;
    font-size: 20rpx;
  }

  .info-item {
    flex-direction: column;
    gap: 12rpx;
    padding: 16rpx;
  }

  .info-label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 8rpx;
  }

  .section-content {
    padding: 28rpx 24rpx;
  }

  .subsection {
    padding: 24rpx 20rpx;
  }

  .list-section {
    padding: 16rpx;
  }
}

/* 内容章节 */
.content-sections {
  margin: 0 24rpx;
}

.section-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.section-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.section-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.section-desc {
  font-size: 24rpx;
  color: #999;
}

.toggle-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.section-content {
  padding: 32rpx;
}

.content-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.content-item {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 4rpx solid #667eea;
}

.item-label {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.item-value {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

.list-group {
  margin-bottom: 32rpx;
}

.list-group:last-child {
  margin-bottom: 0;
}

.list-title {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.list-item {
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  border-left: 3rpx solid #667eea;
}

.list-item:last-child {
  margin-bottom: 0;
}

.item-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.feeding-group {
  margin-bottom: 32rpx;
}

.feeding-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: block;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}
