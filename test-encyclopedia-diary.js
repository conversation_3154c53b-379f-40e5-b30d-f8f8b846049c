// 百科日记功能测试文件

// 模拟微信小程序环境
const mockWx = {
  getStorageSync: function(key) {
    const storage = this._storage || {}
    return storage[key] || null
  },
  setStorageSync: function(key, data) {
    this._storage = this._storage || {}
    this._storage[key] = data
  },
  _storage: {}
}

// 模拟 getApp 函数
global.getApp = function() {
  return {
    globalData: {
      policyConfig: {
        monthlyAmount: 300,
        maxAge: 3,
        policyStartDate: '2025-01-01',
        minBirthDate: '1990-01-01'
      }
    }
  }
}

// 模拟 wx 对象
global.wx = mockWx

// 引入百科日记模块
const encyclopediaDiary = require('./utils/encyclopediaDiary')
const integrateKnowledge = require('./utils/integrateKnowledge')

// 测试函数
function runTests() {
  console.log('开始测试百科日记功能...\n')
  
  // 测试1: 获取特定月龄的百科日记
  console.log('测试1: 获取特定月龄的百科日记')
  const diary0 = encyclopediaDiary.getEncyclopediaDiary(0)
  const diary6 = encyclopediaDiary.getEncyclopediaDiary(6)
  const diary12 = encyclopediaDiary.getEncyclopediaDiary(12)
  
  if (diary0) {
    console.log('✓ 成功获取0月龄日记:', diary0.title)
    console.log('✓ 呵护要求数量:', Object.keys(diary0.careRequirements).length)
    console.log('✓ FAQ数量:', diary0.faqs.length)
  } else {
    console.log('✗ 获取0月龄日记失败')
  }
  
  if (diary6) {
    console.log('✓ 成功获取6月龄日记:', diary6.title)
    console.log('✓ 辅食已引入:', diary6.feeding.complementaryFood.introduced)
    console.log('✓ 推荐食物:', diary6.feeding.complementaryFood.foods.join(', '))
  }
  
  if (diary12) {
    console.log('✓ 成功获取12月龄日记:', diary12.title)
    console.log('✓ 里程碑-身体发育:', diary12.milestones.physical.length, '项')
  }
  
  // 测试2: 获取月龄范围的日记
  console.log('\n测试2: 获取月龄范围的日记')
  const rangeDiaries = encyclopediaDiary.getEncyclopediaDiariesByRange(0, 12)
  console.log('✓ 获取0-12月龄日记数量:', rangeDiaries.length)
  
  // 测试3: 搜索功能
  console.log('\n测试3: 搜索功能')
  const searchResults1 = encyclopediaDiary.searchEncyclopediaDiary('辅食')
  const searchResults2 = encyclopediaDiary.searchEncyclopediaDiary('睡眠')
  const searchResults3 = encyclopediaDiary.searchEncyclopediaDiary('发育')
  
  console.log('✓ 搜索"辅食"结果数量:', searchResults1.length)
  console.log('✓ 搜索"睡眠"结果数量:', searchResults2.length)
  console.log('✓ 搜索"发育"结果数量:', searchResults3.length)
  
  if (searchResults1.length > 0) {
    console.log('  第一个结果:', searchResults1[0].title)
    console.log('  匹配项数量:', searchResults1[0].matches.length)
  }
  
  // 测试4: 月龄标题格式化
  console.log('\n测试4: 月龄标题格式化')
  const titles = [0, 1, 6, 12, 18, 24, 36].map(month => ({
    month,
    title: encyclopediaDiary.getMonthAgeTitle(month)
  }))
  
  titles.forEach(item => {
    console.log(`✓ ${item.month}月龄 -> ${item.title}`)
  })
  
  // 测试5: 月龄分组
  console.log('\n测试5: 月龄分组')
  const groups = encyclopediaDiary.getMonthAgeGroups()
  console.log('✓ 分组数量:', groups.length)
  groups.forEach(group => {
    console.log(`  ${group.icon} ${group.name}: ${group.range[0]}-${group.range[1]}月`)
  })
  
  // 测试6: 整合现有知识
  console.log('\n测试6: 整合现有知识')
  const integratedData = integrateKnowledge.integrateExistingKnowledge()
  const faqCategories = integrateKnowledge.getCommonFAQCategories()
  
  console.log('✓ 整合数据月龄数量:', Object.keys(integratedData).length)
  console.log('✓ FAQ分类数量:', Object.keys(faqCategories).length)
  
  Object.keys(faqCategories).forEach(key => {
    const category = faqCategories[key]
    console.log(`  ${category.icon} ${category.name}: ${category.questions.length}个问题`)
  })
  
  // 测试7: 数据完整性检查
  console.log('\n测试7: 数据完整性检查')
  const testMonths = [0, 6, 12]
  let allValid = true
  
  testMonths.forEach(month => {
    const diary = encyclopediaDiary.getEncyclopediaDiary(month)
    if (diary) {
      const hasRequiredFields = diary.monthAge !== undefined && 
                               diary.title && 
                               diary.careRequirements && 
                               diary.feeding && 
                               diary.milestones
      
      if (hasRequiredFields) {
        console.log(`✓ ${month}月龄数据完整性检查通过`)
      } else {
        console.log(`✗ ${month}月龄数据完整性检查失败`)
        allValid = false
      }
    }
  })
  
  if (allValid) {
    console.log('✓ 所有测试月龄数据完整性检查通过')
  }
  
  // 测试8: 搜索边界情况
  console.log('\n测试8: 搜索边界情况')
  const emptySearch = encyclopediaDiary.searchEncyclopediaDiary('')
  const noResultSearch = encyclopediaDiary.searchEncyclopediaDiary('不存在的关键词xyz123')
  
  console.log('✓ 空搜索结果数量:', emptySearch.length, '(应该为0)')
  console.log('✓ 无结果搜索数量:', noResultSearch.length, '(应该为0)')
  
  // 测试9: 性能测试
  console.log('\n测试9: 性能测试')
  const startTime = Date.now()
  
  // 执行多次搜索操作
  for (let i = 0; i < 100; i++) {
    encyclopediaDiary.searchEncyclopediaDiary('宝宝')
  }
  
  const endTime = Date.now()
  console.log('✓ 100次搜索操作耗时:', endTime - startTime, 'ms')
  
  console.log('\n所有测试完成！')
  
  // 输出功能总结
  console.log('\n=== 百科日记功能总结 ===')
  console.log('✓ 支持的月龄范围: 0-36个月')
  console.log('✓ 当前有数据的月龄:', Object.keys(encyclopediaDiary.getEncyclopediaData()).join(', '))
  console.log('✓ 主要功能: 按月龄浏览、关键词搜索、分类查看')
  console.log('✓ 内容包括: 呵护要求、注意事项、饮食指导、发育里程碑、常见问题')
  console.log('✓ 整合了现有育儿知识和专业指导')
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = { runTests }
} else {
  // 浏览器环境
  runTests()
}

// 如果直接运行此文件
if (require.main === module) {
  runTests()
}
