/* child-manage.wxss - 孩子管理页面样式 */

/* 引入全局样式变量 */
@import "../../app.wxss";

/* 表单容器 */
.form-container {
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.form-section {
  margin-top: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 1px solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background: white;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-input.error {
  border-color: #ef4444;
}

.picker-input {
  width: 100%;
  padding: 24rpx;
  border: 1px solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background: white;
  box-sizing: border-box;
  min-height: 28rpx;
  display: flex;
  align-items: center;
}

.picker-input.error {
  border-color: #ef4444;
}

.placeholder {
  color: var(--text-secondary);
  opacity: 0.6;
}

.error-text {
  font-size: 24rpx;
  color: #ef4444;
  margin-top: 8rpx;
  display: block;
}

/* 操作按钮 */
.actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1px solid var(--border-light);
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 16rpx;
}

.btn-primary, .btn-secondary {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:disabled {
  background: var(--text-secondary);
  opacity: 0.6;
}

.btn-secondary {
  background: var(--bg-light);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

/* 管理模式样式 */
.manage-container {
  
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24rpx;
}

.title-section {
  flex: 1;
}

.children-list {
  margin-top: 32rpx;
}

.child-card {
  margin-bottom: 24rpx;
}

.child-info {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.child-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-icon {
  font-size: 48rpx;
  color: white;
}

.child-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.child-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.child-nickname, .child-birth, .child-age, .child-gender {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.child-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex-shrink: 0;
}

.btn-text {
  background: none;
  border: none;
  font-size: 26rpx;
  color: var(--primary-color);
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  border: 1px solid var(--primary-color);
}

.btn-text.btn-danger {
  color: #ef4444;
  border-color: #ef4444;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  display: block;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 48rpx;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
  }
  
  .child-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .child-actions {
    flex-direction: row;
    justify-content: center;
  }
}
