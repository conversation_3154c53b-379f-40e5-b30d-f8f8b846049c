// 测试完整的百科日记功能
const encyclopediaDiary = require('./utils/encyclopediaDiary')

function testCompleteEncyclopedia() {
  console.log('=== 百科日记完整功能测试 ===\n')
  
  // 测试1: 验证所有月龄数据
  console.log('测试1: 验证0-36个月数据完整性')
  const missingMonths = []
  const availableMonths = []
  
  for (let month = 0; month <= 36; month++) {
    const diary = encyclopediaDiary.getEncyclopediaDiary(month)
    if (diary) {
      availableMonths.push(month)
    } else {
      missingMonths.push(month)
    }
  }
  
  console.log(`✓ 已有数据的月龄: ${availableMonths.length}个`)
  console.log(`✓ 可用月龄: ${availableMonths.join(', ')}`)
  if (missingMonths.length > 0) {
    console.log(`⚠ 缺失数据的月龄: ${missingMonths.join(', ')}`)
  }
  
  // 测试2: 验证月龄标题格式化
  console.log('\n测试2: 月龄标题格式化')
  const testMonths = [0, 1, 6, 12, 18, 24, 36]
  testMonths.forEach(month => {
    const title = encyclopediaDiary.getMonthAgeTitle(month)
    console.log(`${month}个月: ${title}`)
  })
  
  // 测试3: 验证月龄分组
  console.log('\n测试3: 月龄分组功能')
  const groups = encyclopediaDiary.getMonthAgeGroups()
  groups.forEach(group => {
    console.log(`${group.name}: ${group.range[0]}-${group.range[1]}个月`)
  })
  
  // 测试4: 验证搜索功能
  console.log('\n测试4: 搜索功能测试')
  const searchTerms = ['喂养', '睡眠', '发育', '疫苗', '辅食']
  searchTerms.forEach(term => {
    const results = encyclopediaDiary.searchEncyclopediaDiary(term)
    console.log(`搜索"${term}": 找到${results.length}个结果`)
  })
  
  // 测试5: 验证数据结构完整性
  console.log('\n测试5: 数据结构完整性检查')
  const sampleMonths = [0, 6, 12, 24, 36]
  sampleMonths.forEach(month => {
    const diary = encyclopediaDiary.getEncyclopediaDiary(month)
    if (diary) {
      console.log(`\n${month}个月数据结构:`)
      console.log(`- 标题: ${diary.title}`)
      console.log(`- 护理要求: ${Object.keys(diary.careRequirements || {}).length}项`)
      console.log(`- 注意事项: ${Object.keys(diary.precautions || {}).length}项`)
      console.log(`- 喂养指导: ${diary.feeding ? '✓' : '✗'}`)
      console.log(`- 发育里程碑: ${Object.keys(diary.milestones || {}).length}项`)
      console.log(`- 常见问题: ${(diary.faqs || []).length}个`)
    }
  })
  
  // 测试6: 验证内容质量
  console.log('\n测试6: 内容质量检查')
  const qualityCheck = {
    totalEntries: availableMonths.length,
    withFAQs: 0,
    withFeeding: 0,
    withMilestones: 0,
    avgFAQsPerEntry: 0
  }
  
  let totalFAQs = 0
  availableMonths.forEach(month => {
    const diary = encyclopediaDiary.getEncyclopediaDiary(month)
    if (diary.faqs && diary.faqs.length > 0) {
      qualityCheck.withFAQs++
      totalFAQs += diary.faqs.length
    }
    if (diary.feeding) {
      qualityCheck.withFeeding++
    }
    if (diary.milestones) {
      qualityCheck.withMilestones++
    }
  })
  
  qualityCheck.avgFAQsPerEntry = (totalFAQs / qualityCheck.totalEntries).toFixed(1)
  
  console.log('内容质量统计:')
  console.log(`- 总条目数: ${qualityCheck.totalEntries}`)
  console.log(`- 包含FAQ的条目: ${qualityCheck.withFAQs}`)
  console.log(`- 包含喂养指导的条目: ${qualityCheck.withFeeding}`)
  console.log(`- 包含发育里程碑的条目: ${qualityCheck.withMilestones}`)
  console.log(`- 平均FAQ数量: ${qualityCheck.avgFAQsPerEntry}个/条目`)
  
  // 测试7: 性能测试
  console.log('\n测试7: 性能测试')
  const startTime = Date.now()
  
  // 执行100次搜索操作
  for (let i = 0; i < 100; i++) {
    encyclopediaDiary.searchEncyclopediaDiary('发育')
  }
  
  const searchTime = Date.now() - startTime
  console.log(`100次搜索耗时: ${searchTime}ms (平均${(searchTime/100).toFixed(2)}ms/次)`)
  
  // 执行100次数据获取操作
  const getStartTime = Date.now()
  for (let i = 0; i < 100; i++) {
    encyclopediaDiary.getEncyclopediaDiary(Math.floor(Math.random() * 37))
  }
  const getTime = Date.now() - getStartTime
  console.log(`100次数据获取耗时: ${getTime}ms (平均${(getTime/100).toFixed(2)}ms/次)`)
  
  console.log('\n=== 测试完成 ===')
  
  // 返回测试结果
  return {
    totalMonths: availableMonths.length,
    missingMonths: missingMonths.length,
    searchPerformance: searchTime / 100,
    getPerformance: getTime / 100,
    qualityScore: (qualityCheck.withFAQs / qualityCheck.totalEntries * 100).toFixed(1)
  }
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = { testCompleteEncyclopedia }
} else {
  // 浏览器环境
  testCompleteEncyclopedia()
}

// 如果直接运行此文件
if (require.main === module) {
  const results = testCompleteEncyclopedia()
  console.log('\n测试结果摘要:')
  console.log(`- 数据覆盖率: ${results.totalMonths}/37 (${(results.totalMonths/37*100).toFixed(1)}%)`)
  console.log(`- 缺失月龄: ${results.missingMonths}个`)
  console.log(`- 搜索性能: ${results.searchPerformance.toFixed(2)}ms`)
  console.log(`- 获取性能: ${results.getPerformance.toFixed(2)}ms`)
  console.log(`- 内容质量: ${results.qualityScore}%`)
}
