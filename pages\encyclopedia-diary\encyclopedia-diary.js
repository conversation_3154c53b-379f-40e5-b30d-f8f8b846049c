// encyclopedia-diary.js - 百科日记主页面
const encyclopediaDiary = require('../../utils/encyclopediaDiary')

Page({
  data: {
    selectedGroup: 'newborn', // 当前选中的月龄分组
    selectedMonthAge: 0, // 当前选中的具体月龄
    selectedMonthIndex: 0, // 当前选中的月龄在选项中的索引
    monthAgeGroups: [], // 月龄分组
    monthAgeOptions: [], // 所有月龄选项
    currentMonthTitle: '新生儿期（0-1个月）', // 当前月龄标题
    currentDiary: null, // 当前显示的百科日记
    searchQuery: '', // 搜索关键词
    searchResults: [], // 搜索结果
    showSearchResults: false, // 是否显示搜索结果
    loading: false,
    
    // 热门搜索词
    hotSearches: [
      '喂养', '睡眠', '发育', '疫苗', '辅食', '安全',
      '哭闹', '便秘', '发热', '湿疹', '黄疸', '体重'
    ],
    
    // 快速导航
    quickNav: [
      { id: 'feeding', name: '喂养指导', icon: '🍼', monthAge: 0 },
      { id: 'sleep', name: '睡眠指导', icon: '😴', monthAge: 0 },
      { id: 'development', name: '发育里程碑', icon: '📈', monthAge: 0 },
      { id: 'health', name: '健康指标', icon: '🏥', monthAge: 0 },
      { id: 'safety', name: '安全注意', icon: '🛡️', monthAge: 0 },
      { id: 'activities', name: '活动建议', icon: '🎯', monthAge: 6 }
    ]
  },

  onLoad(options) {
    this.initializeData()
    this.loadDefaultDiary()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.selectedMonthAge !== null) {
      this.loadDiary(this.data.selectedMonthAge)
    }
  },

  // 初始化数据
  initializeData() {
    const monthAgeGroups = encyclopediaDiary.getMonthAgeGroups()
    const monthAgeOptions = this.generateMonthAgeOptions()

    this.setData({
      monthAgeGroups,
      monthAgeOptions
    })
  },

  // 生成所有月龄选项
  generateMonthAgeOptions() {
    const options = []

    // 0-36个月的所有选项
    for (let i = 0; i <= 36; i++) {
      options.push({
        value: i,
        title: encyclopediaDiary.getMonthAgeTitle(i)
      })
    }

    return options
  },

  // 加载默认日记（新生儿期）
  loadDefaultDiary() {
    this.loadDiary(0)
  },

  // 加载特定月龄的百科日记
  loadDiary(monthAge) {
    this.setData({ loading: true })

    try {
      const diary = encyclopediaDiary.getEncyclopediaDiary(monthAge)

      if (diary) {
        // 找到对应的月龄索引
        let monthIndex = 0
        const options = this.data.monthAgeOptions
        for (let i = 0; i < options.length; i++) {
          if (options[i].value === monthAge) {
            monthIndex = i
            break
          }
        }
        const currentMonthTitle = encyclopediaDiary.getMonthAgeTitle(monthAge)

        this.setData({
          currentDiary: diary,
          selectedMonthAge: monthAge,
          selectedMonthIndex: monthIndex,
          currentMonthTitle: currentMonthTitle,
          loading: false
        })

        // 更新选中的分组
        this.updateSelectedGroup(monthAge)
      } else {
        wx.showToast({
          title: '暂无该月龄数据',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载百科日记失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 更新选中的分组
  updateSelectedGroup(monthAge) {
    const groups = this.data.monthAgeGroups
    let selectedGroup = 'newborn'

    for (let i = 0; i < groups.length; i++) {
      const group = groups[i]
      if (monthAge >= group.range[0] && monthAge < group.range[1]) {
        selectedGroup = group.id
        break
      }
    }

    this.setData({ selectedGroup })
  },

  // 分组切换
  onGroupChange(e) {
    const groupId = e.currentTarget.dataset.groupId
    const groups = this.data.monthAgeGroups
    let group = null

    for (let i = 0; i < groups.length; i++) {
      if (groups[i].id === groupId) {
        group = groups[i]
        break
      }
    }

    if (group) {
      this.setData({ selectedGroup: groupId })
      // 加载该分组的第一个月龄
      this.loadDiary(group.range[0])
    }
  },

  // 月龄选择
  onMonthAgeChange(e) {
    const monthAge = parseInt(e.currentTarget.dataset.monthAge)
    this.loadDiary(monthAge)
  },

  // 月龄选择器变化
  onMonthPickerChange(e) {
    const index = e.detail.value
    const selectedOption = this.data.monthAgeOptions[index]

    if (selectedOption) {
      this.loadDiary(selectedOption.value)
    }
  },



  // 搜索输入
  onSearchInput(e) {
    const query = e.detail.value
    this.setData({ searchQuery: query })
    
    if (query.trim() === '') {
      this.setData({ 
        showSearchResults: false,
        searchResults: []
      })
      return
    }
    
    // 实时搜索
    this.performSearch(query)
  },

  // 执行搜索
  performSearch(query) {
    try {
      const results = encyclopediaDiary.searchEncyclopediaDiary(query)
      this.setData({
        searchResults: results,
        showSearchResults: true
      })
    } catch (error) {
      console.error('搜索失败:', error)
      wx.showToast({
        title: '搜索失败',
        icon: 'error'
      })
    }
  },

  // 热门搜索点击
  onHotSearchTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ searchQuery: keyword })
    this.performSearch(keyword)
  },

  // 搜索结果点击
  onSearchResultTap(e) {
    const monthAge = e.currentTarget.dataset.monthAge
    this.loadDiary(monthAge)
    this.setData({
      showSearchResults: false,
      searchQuery: ''
    })
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchQuery: '',
      searchResults: [],
      showSearchResults: false
    })
  },

  // 快速导航点击
  onQuickNavTap(e) {
    const dataset = e.currentTarget.dataset
    const id = dataset.id
    const monthAge = dataset.monthAge

    // 加载对应月龄的日记
    this.loadDiary(monthAge)

    // 可以在这里添加滚动到特定章节的逻辑
    setTimeout(() => {
      this.scrollToSection(id)
    }, 500)
  },

  // 滚动到特定章节
  scrollToSection(sectionId) {
    // 检查元素是否存在
    const selector = '#section-' + sectionId

    wx.pageScrollTo({
      selector: selector,
      duration: 300,
      fail: function(error) {
        console.warn('滚动到章节失败:', sectionId, error)
        // 如果特定章节不存在，滚动到内容区域
        wx.pageScrollTo({
          selector: '.content-overview',
          duration: 300,
          fail: function() {
            console.warn('滚动到内容区域也失败')
          }
        })
      }
    })
  },

  // 查看详情
  viewDetail(e) {
    const monthAge = e.currentTarget.dataset.monthAge

    wx.navigateTo({
      url: `/pages/encyclopedia-detail/encyclopedia-detail?monthAge=${monthAge}`
    })
  },

  // 收藏功能
  toggleFavorite(e) {
    const monthAge = e.currentTarget.dataset.monthAge

    // 这里可以实现收藏功能
    wx.showToast({
      title: '收藏成功',
      icon: 'success'
    })
  },

  // 分享功能
  onShareAppMessage() {
    const currentDiary = this.data.currentDiary

    return {
      title: `育儿百科 - ${currentDiary ? currentDiary.title : '育儿指导'}`,
      path: `/pages/encyclopedia-diary/encyclopedia-diary?monthAge=${this.data.selectedMonthAge}`
    }
  },

  onShareTimeline() {
    const currentDiary = this.data.currentDiary

    return {
      title: `育儿百科 - ${currentDiary ? currentDiary.title : '育儿指导'}`
    }
  },

  // 生成月龄列表
  generateMonthAgeList(startMonth, endMonth) {
    const list = []
    for (let i = startMonth; i < endMonth; i++) {
      list.push({
        monthAge: i,
        title: encyclopediaDiary.getMonthAgeTitle(i)
      })
    }
    return list
  }
})
