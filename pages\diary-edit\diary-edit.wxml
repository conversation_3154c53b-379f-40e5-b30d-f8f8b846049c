<!--diary-edit.wxml - 成长日记编辑页面-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body">
        <view class="header-content">
          <view class="title-section">
            <view class="card-title">
              <text class="icon">✏️</text>
              <text>{{mode === 'create' ? '新建' : '编辑'}}{{year}}年{{month}}月日记</text>
            </view>
            <text class="subtitle">记录宝宝这个月的成长点滴</text>
          </view>
          <view class="header-actions">
            <button class="btn-secondary" bindtap="cancelEdit">取消</button>
            <button class="btn-primary" bindtap="saveDiary" disabled="{{saving}}">
              <text wx:if="{{saving}}">保存中...</text>
              <text wx:else>保存</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view wx:if="{{!loading}}" class="form-content">
    <!-- 呵护要求 -->
    <view class="form-section">
      <view class="section-title">
        <text class="icon">💝</text>
        <text>呵护要求</text>
      </view>
      <view class="form-group">
        <text class="form-label">皮肤护理</text>
        <textarea class="form-textarea" 
                  placeholder="记录皮肤护理的要求和注意事项"
                  value="{{formData.careRequirements.skinCare}}"
                  data-field="careRequirements.skinCare"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">口腔护理</text>
        <textarea class="form-textarea" 
                  placeholder="记录口腔护理的要求和注意事项"
                  value="{{formData.careRequirements.oralCare}}"
                  data-field="careRequirements.oralCare"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">洗澡护理</text>
        <textarea class="form-textarea" 
                  placeholder="记录洗澡护理的要求和注意事项"
                  value="{{formData.careRequirements.bathingCare}}"
                  data-field="careRequirements.bathingCare"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">睡眠护理</text>
        <textarea class="form-textarea" 
                  placeholder="记录睡眠护理的要求和注意事项"
                  value="{{formData.careRequirements.sleepCare}}"
                  data-field="careRequirements.sleepCare"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">情感呵护</text>
        <textarea class="form-textarea" 
                  placeholder="记录情感呵护的要求和注意事项"
                  value="{{formData.careRequirements.emotionalCare}}"
                  data-field="careRequirements.emotionalCare"
                  bindinput="onInputChange"></textarea>
      </view>
    </view>

    <!-- 饮食记录 -->
    <view class="form-section">
      <view class="section-title">
        <text class="icon">🍼</text>
        <text>饮食记录</text>
      </view>
      
      <!-- 母乳喂养 -->
      <view class="subsection">
        <text class="subsection-title">母乳喂养</text>
        <view class="form-row">
          <view class="form-group half">
            <text class="form-label">每日次数</text>
            <input class="form-input" 
                   type="number" 
                   placeholder="0"
                   value="{{formData.feeding.breastfeeding.frequency}}"
                   data-field="feeding.breastfeeding.frequency"
                   bindinput="onNumberChange" />
          </view>
          <view class="form-group half">
            <text class="form-label">每次时长(分钟)</text>
            <input class="form-input" 
                   type="number" 
                   placeholder="0"
                   value="{{formData.feeding.breastfeeding.duration}}"
                   data-field="feeding.breastfeeding.duration"
                   bindinput="onNumberChange" />
          </view>
        </view>
        <view class="form-group">
          <text class="form-label">备注</text>
          <textarea class="form-textarea" 
                    placeholder="记录母乳喂养的相关情况"
                    value="{{formData.feeding.breastfeeding.notes}}"
                    data-field="feeding.breastfeeding.notes"
                    bindinput="onInputChange"></textarea>
        </view>
      </view>

      <!-- 奶粉喂养 -->
      <view class="subsection">
        <text class="subsection-title">奶粉喂养</text>
        <view class="form-row">
          <view class="form-group half">
            <text class="form-label">每日次数</text>
            <input class="form-input" 
                   type="number" 
                   placeholder="0"
                   value="{{formData.feeding.formulaFeeding.frequency}}"
                   data-field="feeding.formulaFeeding.frequency"
                   bindinput="onNumberChange" />
          </view>
          <view class="form-group half">
            <text class="form-label">每次奶量(ml)</text>
            <input class="form-input" 
                   type="number" 
                   placeholder="0"
                   value="{{formData.feeding.formulaFeeding.amount}}"
                   data-field="feeding.formulaFeeding.amount"
                   bindinput="onNumberChange" />
          </view>
        </view>
        <view class="form-group">
          <text class="form-label">奶粉品牌</text>
          <input class="form-input" 
                 placeholder="请输入奶粉品牌"
                 value="{{formData.feeding.formulaFeeding.brand}}"
                 data-field="feeding.formulaFeeding.brand"
                 bindinput="onInputChange" />
        </view>
      </view>

      <!-- 辅食 -->
      <view class="subsection">
        <text class="subsection-title">辅食</text>
        <view class="form-group">
          <view class="switch-group">
            <text class="form-label">是否开始添加辅食</text>
            <switch checked="{{formData.feeding.complementaryFood.introduced}}"
                    data-field="feeding.complementaryFood.introduced"
                    bindchange="onSwitchChange" />
          </view>
        </view>
        <view wx:if="{{formData.feeding.complementaryFood.introduced}}" class="form-group">
          <text class="form-label">喂养时间安排</text>
          <textarea class="form-textarea" 
                    placeholder="记录辅食的喂养时间安排"
                    value="{{formData.feeding.complementaryFood.schedule}}"
                    data-field="feeding.complementaryFood.schedule"
                    bindinput="onInputChange"></textarea>
        </view>
      </view>
    </view>

    <!-- 排尿记录 -->
    <view class="form-section">
      <view class="section-title">
        <text class="icon">💧</text>
        <text>排尿记录</text>
      </view>
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">每日次数</text>
          <input class="form-input" 
                 type="number" 
                 placeholder="0"
                 value="{{formData.urination.frequency}}"
                 data-field="urination.frequency"
                 bindinput="onNumberChange" />
        </view>
        <view class="form-group half">
          <text class="form-label">尿液颜色</text>
          <picker range="{{colorOptions}}" 
                  value="{{formData.urination.color}}"
                  data-field="urination.color"
                  data-options="colorOptions"
                  bindchange="onPickerChange">
            <view class="picker-value">{{formData.urination.color || '请选择'}}</view>
          </picker>
        </view>
      </view>
      <view class="form-group">
        <text class="form-label">换尿布次数</text>
        <input class="form-input" 
               type="number" 
               placeholder="0"
               value="{{formData.urination.diaperChanges}}"
               data-field="urination.diaperChanges"
               bindinput="onNumberChange" />
      </view>
      <view class="form-group">
        <text class="form-label">备注</text>
        <textarea class="form-textarea" 
                  placeholder="记录排尿相关的其他情况"
                  value="{{formData.urination.notes}}"
                  data-field="urination.notes"
                  bindinput="onInputChange"></textarea>
      </view>
    </view>

    <!-- 排便记录 -->
    <view class="form-section">
      <view class="section-title">
        <text class="icon">💩</text>
        <text>排便记录</text>
      </view>
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">每日次数</text>
          <input class="form-input" 
                 type="number" 
                 placeholder="0"
                 value="{{formData.defecation.frequency}}"
                 data-field="defecation.frequency"
                 bindinput="onNumberChange" />
        </view>
        <view class="form-group half">
          <text class="form-label">便便性状</text>
          <picker range="{{consistencyOptions}}" 
                  value="{{formData.defecation.consistency}}"
                  data-field="defecation.consistency"
                  data-options="consistencyOptions"
                  bindchange="onPickerChange">
            <view class="picker-value">{{formData.defecation.consistency || '请选择'}}</view>
          </picker>
        </view>
      </view>
      <view class="form-group">
        <text class="form-label">颜色</text>
        <input class="form-input" 
               placeholder="请描述便便颜色"
               value="{{formData.defecation.color}}"
               data-field="defecation.color"
               bindinput="onInputChange" />
      </view>
      <view class="form-group">
        <view class="switch-group">
          <text class="form-label">是否排便困难</text>
          <switch checked="{{formData.defecation.difficulty}}"
                  data-field="defecation.difficulty"
                  bindchange="onSwitchChange" />
        </view>
      </view>
      <view class="form-group">
        <view class="switch-group">
          <text class="form-label">是否有血丝</text>
          <switch checked="{{formData.defecation.blood}}"
                  data-field="defecation.blood"
                  bindchange="onSwitchChange" />
        </view>
      </view>
    </view>

    <!-- 衣服记录 -->
    <view class="form-section">
      <view class="section-title">
        <text class="icon">👕</text>
        <text>衣服记录</text>
      </view>
      <view class="subsection">
        <text class="subsection-title">尺码信息</text>
        <view class="form-row">
          <view class="form-group half">
            <text class="form-label">上衣尺码</text>
            <input class="form-input" 
                   placeholder="如：80cm"
                   value="{{formData.clothing.sizes.tops}}"
                   data-field="clothing.sizes.tops"
                   bindinput="onInputChange" />
          </view>
          <view class="form-group half">
            <text class="form-label">下装尺码</text>
            <input class="form-input" 
                   placeholder="如：80cm"
                   value="{{formData.clothing.sizes.bottoms}}"
                   data-field="clothing.sizes.bottoms"
                   bindinput="onInputChange" />
          </view>
        </view>
        <view class="form-row">
          <view class="form-group half">
            <text class="form-label">鞋子尺码</text>
            <input class="form-input" 
                   placeholder="如：13cm"
                   value="{{formData.clothing.sizes.shoes}}"
                   data-field="clothing.sizes.shoes"
                   bindinput="onInputChange" />
          </view>
          <view class="form-group half">
            <text class="form-label">帽子尺码</text>
            <input class="form-input" 
                   placeholder="如：46cm"
                   value="{{formData.clothing.sizes.hats}}"
                   data-field="clothing.sizes.hats"
                   bindinput="onInputChange" />
          </view>
        </view>
      </view>
      <view class="form-group">
        <text class="form-label">特殊需求</text>
        <textarea class="form-textarea" 
                  placeholder="记录过敏材质、特殊要求等"
                  value="{{formData.clothing.specialNeeds}}"
                  data-field="clothing.specialNeeds"
                  bindinput="onInputChange"></textarea>
      </view>
    </view>

    <!-- 家长心得 -->
    <view class="form-section">
      <view class="section-title">
        <text class="icon">💭</text>
        <text>家长心得</text>
      </view>
      <view class="form-group">
        <text class="form-label">观察记录</text>
        <textarea class="form-textarea" 
                  placeholder="记录这个月观察到的变化和发现"
                  value="{{formData.parentNotes.observations}}"
                  data-field="parentNotes.observations"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">成就记录</text>
        <textarea class="form-textarea" 
                  placeholder="记录宝宝这个月的成就和进步"
                  value="{{formData.parentNotes.achievements}}"
                  data-field="parentNotes.achievements"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">遇到的挑战</text>
        <textarea class="form-textarea" 
                  placeholder="记录育儿过程中遇到的困难和挑战"
                  value="{{formData.parentNotes.challenges}}"
                  data-field="parentNotes.challenges"
                  bindinput="onInputChange"></textarea>
      </view>
      <view class="form-group">
        <text class="form-label">育儿心得</text>
        <textarea class="form-textarea" 
                  placeholder="分享育儿经验和心得体会"
                  value="{{formData.parentNotes.tips}}"
                  data-field="parentNotes.tips"
                  bindinput="onInputChange"></textarea>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 底部保存按钮 -->
  <view class="bottom-actions">
    <button class="btn-save" bindtap="saveDiary" disabled="{{saving}}">
      <text wx:if="{{saving}}">保存中...</text>
      <text wx:else>保存日记</text>
    </button>
  </view>
</view>
