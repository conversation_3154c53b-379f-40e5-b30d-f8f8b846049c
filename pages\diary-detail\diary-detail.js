// diary-detail.js - 成长日记详情页面
const growthDiary = require('../../utils/growthDiary')

Page({
  data: {
    childId: '',
    year: 0,
    month: 0,
    diary: null,
    loading: true,
    isFavorited: false,

    // 展开状态控制
    expandedSections: {
      careRequirements: false,
      precautions: false,
      feeding: false,
      urination: false,
      defecation: false,
      clothing: false,
      milestones: false,
      health: false,
      sleep: false,
      activities: false,
      photos: false,
      parentNotes: false
    }
  },

  onLoad(options) {
    console.log('diary-detail onLoad options:', options)

    // 提供默认值以便测试
    const childId = options.childId || 'default-child'
    const year = options.year || new Date().getFullYear()
    const month = options.month || (new Date().getMonth() + 1)

    console.log('Parameters:', { childId, year, month })

    this.setData({
      childId,
      year: parseInt(year),
      month: parseInt(month)
    })
    
    this.loadDiary()
  },

  // 加载日记数据
  loadDiary() {
    this.setData({ loading: true })
    
    try {
      const diary = growthDiary.getGrowthDiary(
        this.data.childId,
        this.data.year,
        this.data.month
      )
      
      if (!diary) {
        // 提供默认数据以便测试
        const defaultDiary = {
          createTime: new Date().toLocaleDateString(),
          dayOfMonth: 1,
          totalSections: 6,
          careRequirements: {
            skinCare: '保持皮肤清洁干燥，避免过度清洁',
            oralCare: '用温水轻轻清洁口腔，注意动作轻柔',
            bathingCare: '每天洗澡，水温控制在37-40度',
            sleepCare: '保证充足睡眠，营造安静的睡眠环境'
          },
          precautions: {
            safety: ['避免剧烈摇晃婴儿', '注意保暖防寒', '确保睡眠安全'],
            health: ['密切观察体温变化', '注意喂养量和频次', '观察大小便情况']
          },
          feeding: {
            breastfeeding: { frequency: 8, duration: 15 },
            formulaFeeding: { frequency: 6, amount: 60 }
          }
        }

        this.setData({
          diary: defaultDiary,
          loading: false
        })
        return
      }

      this.setData({
        diary,
        loading: false
      })
    } catch (error) {
      console.error('加载日记失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 切换章节展开状态
  toggleSection(e) {
    const section = e.currentTarget.dataset.section
    const key = 'expandedSections.' + section

    const updateData = {}
    updateData[key] = !this.data.expandedSections[section]

    this.setData(updateData)
  },

  // 编辑日记
  editDiary() {
    const url = `/pages/diary-edit/diary-edit?childId=${this.data.childId}&year=${this.data.year}&month=${this.data.month}&mode=edit`
    wx.navigateTo({ url })
  },

  // 删除日记
  deleteDiary() {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除${this.data.year}年${this.data.month}月的成长日记吗？`,
      success: (res) => {
        if (res.confirm) {
          const success = growthDiary.deleteGrowthDiary(
            this.data.childId,
            this.data.year,
            this.data.month
          )
          
          if (success) {
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          } else {
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 预览图片
  previewImage(e) {
    const dataset = e.currentTarget.dataset
    const current = dataset.current
    const urls = dataset.urls
    
    wx.previewImage({
      current,
      urls
    })
  },

  // 复制文本
  copyText(e) {
    const text = e.currentTarget.dataset.text
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `${this.data.year}年${this.data.month}月成长日记`,
      path: `/pages/diary-detail/diary-detail?childId=${this.data.childId}&year=${this.data.year}&month=${this.data.month}`
    }
  },

  onShareTimeline() {
    return {
      title: `${this.data.year}年${this.data.month}月成长日记`
    }
  },

  // 返回列表
  backToList() {
    wx.navigateBack()
  },

  // 显示更多选项
  showMore() {
    wx.showActionSheet({
      itemList: ['分享日记', '导出PDF', '打印日记'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.shareDiary()
            break
          case 1:
            this.exportPDF()
            break
          case 2:
            this.printDiary()
            break
        }
      }
    })
  },

  // 编辑日记
  editDiary() {
    wx.navigateTo({
      url: `/pages/diary-edit/diary-edit?childId=${this.data.childId}&year=${this.data.year}&month=${this.data.month}`
    })
  },

  // 分享日记
  shareDiary() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 导出PDF
  exportPDF() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 打印日记
  printDiary() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 切换收藏状态
  toggleFavorite() {
    const newFavoriteStatus = !this.data.isFavorited
    this.setData({
      isFavorited: newFavoriteStatus
    })

    wx.showToast({
      title: newFavoriteStatus ? '已收藏' : '已取消收藏',
      icon: 'success'
    })
  }
})
