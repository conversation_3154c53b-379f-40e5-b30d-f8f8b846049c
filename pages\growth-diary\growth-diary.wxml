<!--growth-diary.wxml - 成长日记主页面-->
<wxs module="utils">
  function formatAge(birthDate) {
    if (!birthDate) return '未设置出生日期'

    var birth = getDate(birthDate)
    var now = getDate()

    var years = now.getFullYear() - birth.getFullYear()
    var months = now.getMonth() - birth.getMonth()
    var days = now.getDate() - birth.getDate()

    if (days < 0) {
      months--
      var lastMonth = getDate(now.getFullYear(), now.getMonth(), 0)
      days += lastMonth.getDate()
    }

    if (months < 0) {
      years--
      months += 12
    }

    if (years > 0) {
      return years + '岁' + months + '个月'
    } else if (months > 0) {
      return months + '个月' + days + '天'
    } else {
      return days + '天'
    }
  }

  module.exports = {
    formatAge: formatAge
  }
</wxs>

<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body">
        <view class="header-content">
          <view class="title-section">
            <view class="card-title">
              <text class="icon">📖</text>
              <text>成长日记</text>
            </view>
            <text class="subtitle">记录宝宝每个月的成长点滴</text>
          </view>
          
          <!-- 孩子选择器 -->
          <view class="child-selector" bindtap="showChildSelector" wx:if="{{currentChild}}">
            <view class="child-info">
              <text class="child-name">{{currentChild.name}}</text>
              <text class="child-age">{{utils.formatAge(currentChild.birthDate)}}</text>
            </view>
            <text class="icon-arrow">▼</text>
          </view>

          <!-- 无孩子时的添加按钮 -->
          <view class="add-child-btn" bindtap="showAddChildDialog" wx:if="{{!currentChild}}">
            <text class="icon">👶</text>
            <text>添加宝宝</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-number">{{stats.totalDiaries}}</view>
        <view class="stat-label">总记录数</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{stats.currentMonthDiary ? '已记录' : '未记录'}}</view>
        <view class="stat-label">本月状态</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{stats.recentActivity.length}}</view>
        <view class="stat-label">最近活动</view>
      </view>
    </view>
  </view>

  <!-- 月份选择器 -->
  <view class="month-selector">
    <view class="card">
      <view class="card-body">
        <view class="selector-row">
          <view class="selector-item">
            <text class="selector-label">年份：</text>
            <picker range="{{years}}" range-key="label" value="{{currentYear}}" bindchange="onYearChange">
              <view class="picker-value">{{currentYear}}年</view>
            </picker>
          </view>
          <view class="selector-item">
            <text class="selector-label">月份：</text>
            <picker range="{{months}}" range-key="label" value="{{currentMonth - 1}}" bindchange="onMonthChange">
              <view class="picker-value">{{currentMonth}}月</view>
            </picker>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 当前月份日记 -->
  <view class="current-diary">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📅</text>
          <text>{{currentYear}}年{{currentMonth}}月日记</text>
        </view>
        <view class="header-actions">
          <button class="btn-primary" bindtap="createNewDiary" wx:if="{{!stats.currentMonthDiary}}">
            <text class="icon">✏️</text>
            <text>新建</text>
          </button>
          <button class="btn-secondary" bindtap="editDiary" 
                  data-year="{{currentYear}}" 
                  data-month="{{currentMonth}}" 
                  wx:if="{{stats.currentMonthDiary}}">
            <text class="icon">✏️</text>
            <text>编辑</text>
          </button>
        </view>
      </view>
      <view class="card-body">
        <view wx:if="{{stats.currentMonthDiary}}" class="diary-preview">
          <view class="preview-item" bindtap="viewDiary" 
                data-year="{{currentYear}}" 
                data-month="{{currentMonth}}">
            <view class="preview-content">
              <text class="preview-title">点击查看详细记录</text>
              <text class="preview-time">更新时间：{{stats.currentMonthDiary.updateTime}}</text>
            </view>
            <text class="icon-arrow">→</text>
          </view>
        </view>
        <view wx:else class="empty-state">
          <text class="empty-icon">📝</text>
          <text class="empty-text">本月还没有记录，点击新建开始记录吧！</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📚</text>
          <text>历史记录</text>
        </view>
      </view>
      <view class="card-body">
        <view wx:if="{{diaries.length > 0}}" class="diary-list">
          <view wx:for="{{diaries}}" wx:key="id" class="diary-item">
            <view class="diary-info" bindtap="viewDiary" 
                  data-year="{{item.year}}" 
                  data-month="{{item.month}}">
              <view class="diary-date">
                <text class="month">{{item.month}}月</text>
                <text class="year">{{item.year}}</text>
              </view>
              <view class="diary-content">
                <text class="diary-title">{{item.year}}年{{item.month}}月成长记录</text>
                <text class="diary-time">{{item.updateTime}}</text>
              </view>
              <text class="icon-arrow">→</text>
            </view>
            <view class="diary-actions">
              <button class="btn-text" bindtap="editDiary" 
                      data-year="{{item.year}}" 
                      data-month="{{item.month}}">编辑</button>
              <button class="btn-text btn-danger" bindtap="deleteDiary" 
                      data-year="{{item.year}}" 
                      data-month="{{item.month}}">删除</button>
            </view>
          </view>
        </view>
        <view wx:else class="empty-state">
          <text class="empty-icon">📖</text>
          <text class="empty-text">还没有历史记录，开始记录宝宝的成长吧！</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <text class="loading-icon">⏳</text>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</view>

<!-- 孩子选择器弹窗 -->
<view wx:if="{{showChildSelector}}" class="modal-overlay" bindtap="hideChildSelector">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">选择宝宝</text>
      <button class="modal-close" bindtap="hideChildSelector">×</button>
    </view>
    <view class="modal-body">
      <view class="children-list">
        <view wx:for="{{children}}" wx:key="id" class="child-option"
              bindtap="onChildChange" data-index="{{index}}">
          <view class="child-avatar">
            <text class="avatar-icon">👶</text>
          </view>
          <view class="child-details">
            <text class="child-name">{{item.name}}</text>
            <text class="child-birth">出生日期：{{item.birthDate || '未设置'}}</text>
            <text class="child-age">年龄：{{utils.formatAge(item.birthDate)}}</text>
          </view>
          <view class="child-selected" wx:if="{{currentChild && currentChild.id === item.id}}">
            <text class="selected-icon">✓</text>
          </view>
        </view>
      </view>

      <view class="modal-actions">
        <button class="btn-secondary" bindtap="manageChildren">管理宝宝</button>
        <button class="btn-primary" bindtap="navigateToAddChild">添加宝宝</button>
      </view>
    </view>
  </view>
</view>
