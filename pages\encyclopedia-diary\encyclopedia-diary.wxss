/* encyclopedia-diary.wxss - 百科日记主页面样式 */

/* 引入全局样式变量 */
@import "../../app.wxss";

/* 搜索栏样式 */
.search-section {
  margin-bottom: 32rpx;
}

.search-bar {
  margin-bottom: 24rpx;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12rpx;
  border: 1px solid var(--border-light);
  padding: 0 48rpx 0 16rpx;
}

.search-input {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  border: none;
  outline: none;
}

.search-icon {
  position: absolute;
  right: 48rpx;
  font-size: 32rpx;
  color: var(--text-secondary);
}

.clear-icon {
  position: absolute;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: var(--text-secondary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  cursor: pointer;
}

/* 热门搜索 */
.hot-searches {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-title {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-right: 16rpx;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.hot-tag {
  padding: 8rpx 16rpx;
  background: var(--bg-light);
  color: var(--text-secondary);
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1px solid var(--border-light);
}

/* 搜索结果 */
.search-results {
  margin-bottom: 32rpx;
}

.search-result-item {
  padding: 24rpx 0;
  border-bottom: 1px solid var(--border-light);
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.result-matches {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.match-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.match-type {
  padding: 4rpx 8rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 6rpx;
  font-size: 20rpx;
  flex-shrink: 0;
}

.match-content {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.no-results {
  text-align: center;
  padding: 80rpx 40rpx;
}

.no-results-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.no-results-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 快速导航 */
.quick-nav {
  margin-bottom: 32rpx;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
  text-align: center;
}

.nav-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.nav-name {
  font-size: 26rpx;
  font-weight: 500;
}

/* 年龄分组 */
.age-groups {
  margin-bottom: 32rpx;
}

.group-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.group-tab {
  flex: 1;
  min-width: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: var(--bg-light);
  border: 2px solid var(--border-light);
  border-radius: 12rpx;
  text-align: center;
}

.group-tab.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.group-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.group-name {
  font-size: 26rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.group-range {
  font-size: 22rpx;
  opacity: 0.8;
}

/* 月龄选择 */
.month-selector {
  margin-bottom: 32rpx;
}

.month-picker-container {
  margin-bottom: 24rpx;
}

.month-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: white;
  border: 2px solid var(--primary-color);
  border-radius: 12rpx;
  min-height: 28rpx;
}

.picker-text {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--primary-color);
}

.picker-arrow {
  font-size: 24rpx;
  color: var(--primary-color);
}



.quick-btn:active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 日记内容 */
.diary-content {
  margin-bottom: 32rpx;
}

.header-actions {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.btn-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--bg-light);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.btn-primary {
  padding: 12rpx 24rpx;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

/* 内容章节 */
.content-section {
  margin-bottom: 32rpx;
}

.content-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px solid var(--border-light);
}

.section-title .icon {
  font-size: 28rpx;
}

.section-title text:last-child {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.section-content {
  padding-left: 40rpx;
}

/* 信息项 */
.info-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 120rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.6;
  flex: 1;
}

/* 里程碑 */
.milestone-category {
  margin-bottom: 20rpx;
}

.milestone-category:last-child {
  margin-bottom: 0;
}

.milestone-title {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
  display: block;
}

.milestone-item {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.5;
}

/* FAQ */
.faq-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: var(--bg-light);
  border-radius: 12rpx;
  border: 1px solid var(--border-light);
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-question {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.faq-answer {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.faq-tips {
  font-size: 24rpx;
  color: var(--primary-color);
  background: rgba(79, 70, 229, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
}

/* 加载和空状态 */
.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-icon, .empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .group-tabs {
    flex-direction: column;
  }
  
  .group-tab {
    flex: none;
  }
  
  .month-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 安全注意事项 */
.safety-list {

}

.safety-item {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.safety-item text {
  color: var(--text-secondary);
}
