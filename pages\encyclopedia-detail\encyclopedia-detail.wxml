<!--encyclopedia-detail.wxml - 百科详情页面-->
<view class="page">
  <!-- 主内容区 -->
  <view class="main-content">
    <!-- 头部信息卡片 -->
    <view class="info-card">
      <view class="card-body">
        <view class="title">{{diary ? diary.title : '新生儿期（0-1个月）'}}</view>
        <view class="description">宝宝是独特的，内容仅供参考</view>

        <view class="meta-info">
          <view class="meta-item">
            <text class="meta-label">适用年龄</text>
            <text class="meta-value">0-1月龄</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">内容章节</text>
            <text class="meta-value">{{sectionCount}}个章节</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">阅读时长</text>
            <text class="meta-value">约15分钟</text>
          </view>
        </view>
      </view>
    </view>

  </view>

  <!-- 内容区域 -->
  <view wx:if="{{!loading && diary}}" class="content-area">
    <!-- 呵护要求 -->
    <view id="section-careRequirements" class="content-section">
      <view class="modern-card">
        <view class="card-header" bindtap="toggleSection" data-section="careRequirements">
          <view class="header-left">
            <view class="icon-container care-icon">
              <text class="section-emoji">💝</text>
            </view>
            <view class="header-text">
              <text class="section-title">呵护要求</text>
              <text class="section-desc">宝宝日常护理指导</text>
            </view>
          </view>
          <view class="toggle-button {{expandedSections.careRequirements ? 'expanded' : ''}}">
            <text class="toggle-text">{{expandedSections.careRequirements ? '收起' : '展开'}}</text>
            <text class="toggle-arrow">{{expandedSections.careRequirements ? '↑' : '↓'}}</text>
          </view>
        </view>

        <view wx:if="{{expandedSections.careRequirements}}" class="card-body">
          <view class="care-list">
            <view class="care-item" wx:if="{{diary.careRequirements.skinCare}}">
              <view class="item-icon">🧴</view>
              <view class="item-content">
                <text class="item-title">皮肤护理</text>
                <text class="item-text">{{diary.careRequirements.skinCare}}</text>
              </view>
            </view>
            <view class="care-item" wx:if="{{diary.careRequirements.oralCare}}">
              <view class="item-icon">🦷</view>
              <view class="item-content">
                <text class="item-title">口腔护理</text>
                <text class="item-text">{{diary.careRequirements.oralCare}}</text>
              </view>
            </view>
            <view class="care-item" wx:if="{{diary.careRequirements.bathingCare}}">
              <view class="item-icon">🛁</view>
              <view class="item-content">
                <text class="item-title">洗澡护理</text>
                <text class="item-text">{{diary.careRequirements.bathingCare}}</text>
              </view>
            </view>
            <view class="care-item" wx:if="{{diary.careRequirements.sleepCare}}">
              <view class="item-icon">😴</view>
              <view class="item-content">
                <text class="item-title">睡眠护理</text>
                <text class="item-text">{{diary.careRequirements.sleepCare}}</text>
              </view>
            </view>
            <view class="care-item" wx:if="{{diary.careRequirements.exerciseCare}}">
              <view class="item-icon">🤸</view>
              <view class="item-content">
                <text class="item-title">运动护理</text>
                <text class="item-text">{{diary.careRequirements.exerciseCare}}</text>
              </view>
            </view>
            <view class="care-item" wx:if="{{diary.careRequirements.emotionalCare}}">
              <view class="item-icon">🤗</view>
              <view class="item-content">
                <text class="item-title">情感呵护</text>
                <text class="item-text">{{diary.careRequirements.emotionalCare}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 注意事项 -->
    <view id="section-precautions" class="content-section">
      <view class="modern-card">
        <view class="card-header" bindtap="toggleSection" data-section="precautions">
          <view class="header-left">
            <view class="icon-container warning-icon">
              <text class="section-emoji">⚠️</text>
            </view>
            <view class="header-text">
              <text class="section-title">注意事项</text>
              <text class="section-desc">重要安全和健康提醒</text>
            </view>
          </view>
          <view class="toggle-button {{expandedSections.precautions ? 'expanded' : ''}}">
            <text class="toggle-text">{{expandedSections.precautions ? '收起' : '展开'}}</text>
            <text class="toggle-arrow">{{expandedSections.precautions ? '↑' : '↓'}}</text>
          </view>
        </view>

        <view wx:if="{{expandedSections.precautions}}" class="card-body">
          <view class="precaution-groups">
            <view class="precaution-group" wx:if="{{diary.precautions.safety.length > 0}}">
              <view class="group-header">
                <view class="group-icon">🛡️</view>
                <text class="group-title">安全注意事项</text>
              </view>
              <view class="alert-list">
                <view wx:for="{{diary.precautions.safety}}" wx:key="index" class="alert-item safety">
                  <view class="alert-dot"></view>
                  <text class="alert-text">{{item}}</text>
                </view>
              </view>
            </view>

            <view class="precaution-group" wx:if="{{diary.precautions.health.length > 0}}">
              <view class="group-header">
                <view class="group-icon">🏥</view>
                <text class="group-title">健康注意事项</text>
              </view>
              <view class="alert-list">
                <view wx:for="{{diary.precautions.health}}" wx:key="index" class="alert-item health">
                  <view class="alert-dot"></view>
                  <text class="alert-text">{{item}}</text>
                </view>
              </view>
            </view>

            <view class="precaution-group" wx:if="{{diary.precautions.emergency.length > 0}}">
              <view class="group-header">
                <view class="group-icon">🚨</view>
                <text class="group-title">紧急情况</text>
              </view>
              <view class="alert-list">
                <view wx:for="{{diary.precautions.emergency}}" wx:key="index" class="alert-item emergency">
                  <view class="alert-dot emergency-dot"></view>
                  <text class="alert-text">{{item}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 饮食指导 -->
    <view id="section-feeding" class="content-section">
      <view class="modern-card">
        <view class="card-header" bindtap="toggleSection" data-section="feeding">
          <view class="header-left">
            <view class="icon-container feeding-icon">
              <text class="section-emoji">🍼</text>
            </view>
            <view class="header-text">
              <text class="section-title">饮食指导</text>
              <text class="section-desc">科学喂养方案和建议</text>
            </view>
          </view>
          <view class="toggle-button {{expandedSections.feeding ? 'expanded' : ''}}">
            <text class="toggle-text">{{expandedSections.feeding ? '收起' : '展开'}}</text>
            <text class="toggle-arrow">{{expandedSections.feeding ? '↑' : '↓'}}</text>
          </view>
        </view>

        <view wx:if="{{expandedSections.feeding}}" class="card-body">
          <view class="feeding-sections">
            <!-- 母乳喂养 -->
            <view class="feeding-section" wx:if="{{diary.feeding.breastfeeding.frequency}}">
              <view class="feeding-header">
                <view class="feeding-icon">🤱</view>
                <text class="feeding-title">母乳喂养</text>
                <view class="feeding-badge recommended">推荐</view>
              </view>
              <view class="feeding-stats">
                <view class="stat-card">
                  <view class="stat-icon">⏰</view>
                  <view class="stat-info">
                    <text class="stat-label">喂养频次</text>
                    <text class="stat-value">{{diary.feeding.breastfeeding.frequency}}次/天</text>
                  </view>
                </view>
                <view class="stat-card" wx:if="{{diary.feeding.breastfeeding.duration}}">
                  <view class="stat-icon">⏱️</view>
                  <view class="stat-info">
                    <text class="stat-label">单次时长</text>
                    <text class="stat-value">{{diary.feeding.breastfeeding.duration}}分钟</text>
                  </view>
                </view>
              </view>
              <view class="feeding-tips" wx:if="{{diary.feeding.breastfeeding.tips}}">
                <view class="tips-header">
                  <view class="tips-icon">💡</view>
                  <text class="tips-title">喂养技巧</text>
                </view>
                <text class="tips-content">{{diary.feeding.breastfeeding.tips}}</text>
              </view>
            </view>

            <!-- 配方奶喂养 -->
            <view class="feeding-section" wx:if="{{diary.feeding.formulaFeeding.frequency}}">
              <view class="feeding-header">
                <view class="feeding-icon">🍼</view>
                <text class="feeding-title">配方奶喂养</text>
                <view class="feeding-badge alternative">备选</view>
              </view>
              <view class="feeding-stats">
                <view class="stat-card">
                  <view class="stat-icon">⏰</view>
                  <view class="stat-info">
                    <text class="stat-label">喂养频次</text>
                    <text class="stat-value">{{diary.feeding.formulaFeeding.frequency}}次/天</text>
                  </view>
                </view>
                <view class="stat-card" wx:if="{{diary.feeding.formulaFeeding.amount}}">
                  <view class="stat-icon">🥛</view>
                  <view class="stat-info">
                    <text class="stat-label">单次奶量</text>
                    <text class="stat-value">{{diary.feeding.formulaFeeding.amount}}ml</text>
                  </view>
                </view>
              </view>
              <view class="feeding-tips" wx:if="{{diary.feeding.formulaFeeding.preparation}}">
                <view class="tips-header">
                  <view class="tips-icon">🔧</view>
                  <text class="tips-title">冲调方法</text>
                </view>
                <text class="tips-content">{{diary.feeding.formulaFeeding.preparation}}</text>
              </view>
            </view>

            <!-- 营养补充 -->
            <view class="feeding-section" wx:if="{{diary.feeding.supplements.vitaminD || diary.feeding.supplements.iron}}">
              <view class="feeding-header">
                <view class="feeding-icon">💊</view>
                <text class="feeding-title">营养补充</text>
                <view class="feeding-badge supplement">补充</view>
              </view>
              <view class="supplement-list">
                <view class="supplement-item" wx:if="{{diary.feeding.supplements.vitaminD}}">
                  <view class="supplement-icon">☀️</view>
                  <view class="supplement-info">
                    <text class="supplement-name">维生素D</text>
                    <text class="supplement-desc">{{diary.feeding.supplements.vitaminD}}</text>
                  </view>
                </view>
                <view class="supplement-item" wx:if="{{diary.feeding.supplements.iron}}">
                  <view class="supplement-icon">🩸</view>
                  <view class="supplement-info">
                    <text class="supplement-name">铁剂</text>
                    <text class="supplement-desc">{{diary.feeding.supplements.iron}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 发育里程碑 -->
    <view id="section-milestones" class="content-section">
      <view class="modern-card">
        <view class="card-header" bindtap="toggleSection" data-section="milestones">
          <view class="header-left">
            <view class="icon-container milestone-icon">
              <text class="section-emoji">📈</text>
            </view>
            <view class="header-text">
              <text class="section-title">发育里程碑</text>
              <text class="section-desc">宝宝成长发育关键指标</text>
            </view>
          </view>
          <view class="toggle-button {{expandedSections.milestones ? 'expanded' : ''}}">
            <text class="toggle-text">{{expandedSections.milestones ? '收起' : '展开'}}</text>
            <text class="toggle-arrow">{{expandedSections.milestones ? '↑' : '↓'}}</text>
          </view>
        </view>

        <view wx:if="{{expandedSections.milestones}}" class="card-body">
          <view class="milestone-categories">
            <view wx:if="{{diary.milestones.physical.length > 0}}" class="milestone-category">
              <view class="category-header">
                <view class="category-icon physical">💪</view>
                <view class="category-info">
                  <text class="category-title">身体发育</text>
                  <text class="category-desc">运动能力和体格发展</text>
                </view>
                <view class="progress-indicator">
                  <text class="progress-text">{{diary.milestones.physical.length}}项</text>
                </view>
              </view>
              <view class="milestone-list">
                <view wx:for="{{diary.milestones.physical}}" wx:key="index" class="milestone-achievement">
                  <view class="achievement-icon">✓</view>
                  <text class="achievement-text">{{item}}</text>
                </view>
              </view>
            </view>

            <view wx:if="{{diary.milestones.cognitive.length > 0}}" class="milestone-category">
              <view class="category-header">
                <view class="category-icon cognitive">🧠</view>
                <view class="category-info">
                  <text class="category-title">认知发育</text>
                  <text class="category-desc">思维和学习能力发展</text>
                </view>
                <view class="progress-indicator">
                  <text class="progress-text">{{diary.milestones.cognitive.length}}项</text>
                </view>
              </view>
              <view class="milestone-list">
                <view wx:for="{{diary.milestones.cognitive}}" wx:key="index" class="milestone-achievement">
                  <view class="achievement-icon">✓</view>
                  <text class="achievement-text">{{item}}</text>
                </view>
              </view>
            </view>

            <view wx:if="{{diary.milestones.language.length > 0}}" class="milestone-category">
              <view class="category-header">
                <view class="category-icon language">💬</view>
                <view class="category-info">
                  <text class="category-title">语言发育</text>
                  <text class="category-desc">沟通和表达能力发展</text>
                </view>
                <view class="progress-indicator">
                  <text class="progress-text">{{diary.milestones.language.length}}项</text>
                </view>
              </view>
              <view class="milestone-list">
                <view wx:for="{{diary.milestones.language}}" wx:key="index" class="milestone-achievement">
                  <view class="achievement-icon">✓</view>
                  <text class="achievement-text">{{item}}</text>
                </view>
              </view>
            </view>

            <view wx:if="{{diary.milestones.social.length > 0}}" class="milestone-category">
              <view class="category-header">
                <view class="category-icon social">👥</view>
                <view class="category-info">
                  <text class="category-title">社交发育</text>
                  <text class="category-desc">社会交往能力发展</text>
                </view>
                <view class="progress-indicator">
                  <text class="progress-text">{{diary.milestones.social.length}}项</text>
                </view>
              </view>
              <view class="milestone-list">
                <view wx:for="{{diary.milestones.social}}" wx:key="index" class="milestone-achievement">
                  <view class="achievement-icon">✓</view>
                  <text class="achievement-text">{{item}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 常见问题 -->
    <view id="section-faqs" class="content-section" wx:if="{{diary.faqs.length > 0}}">
      <view class="modern-card">
        <view class="card-header" bindtap="toggleSection" data-section="faqs">
          <view class="header-left">
            <view class="icon-container faq-icon">
              <text class="section-emoji">❓</text>
            </view>
            <view class="header-text">
              <text class="section-title">常见问题</text>
              <text class="section-desc">新手父母常见疑问解答</text>
            </view>
          </view>
          <view class="toggle-button {{expandedSections.faqs ? 'expanded' : ''}}">
            <text class="toggle-text">{{expandedSections.faqs ? '收起' : '展开'}}</text>
            <text class="toggle-arrow">{{expandedSections.faqs ? '↑' : '↓'}}</text>
          </view>
        </view>

        <view wx:if="{{expandedSections.faqs}}" class="card-body">
          <view class="faq-list">
            <view wx:for="{{diary.faqs}}" wx:key="index" class="faq-card">
              <view class="question-section">
                <view class="question-header">
                  <view class="question-badge">Q{{index + 1}}</view>
                  <text class="question-title">{{item.question}}</text>
                </view>
              </view>
              <view class="answer-section">
                <view class="answer-header">
                  <view class="answer-badge">A</view>
                  <view class="answer-content">
                    <text class="answer-text">{{item.answer}}</text>
                  </view>
                </view>
                <view wx:if="{{item.tips}}" class="answer-tips">
                  <view class="tips-badge">💡</view>
                  <text class="tips-text">{{item.tips}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && !diary}}" class="empty-state">
    <text class="empty-icon">📚</text>
    <text class="empty-text">暂无该月龄的百科数据</text>
  </view>

  <!-- 底部操作栏 -->
  <view wx:if="{{!loading && diary}}" class="bottom-action-bar">
    <button class="action-btn share-btn" bindtap="sharePage">
      <view class="btn-icon">📤</view>
      <text class="btn-text">分享</text>
    </button>
    <button class="action-btn favorite-btn {{isFavorited ? 'favorited' : ''}}" bindtap="toggleFavorite">
      <view class="btn-icon">{{isFavorited ? '♥' : '♡'}}</view>
      <text class="btn-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
    </button>
  </view>
</view>