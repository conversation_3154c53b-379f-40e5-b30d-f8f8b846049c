// 孩子管理工具函数
const app = getApp()

/**
 * 孩子数据模型
 */
const ChildModel = {
  id: '', // 唯一标识符
  name: '', // 孩子姓名
  birthDate: '', // 出生日期
  gender: '', // 性别
  nickname: '', // 昵称
  avatar: '', // 头像
  createTime: '', // 创建时间
  updateTime: '' // 更新时间
}

/**
 * 生成孩子唯一ID
 * @returns {string} 唯一ID
 */
function generateChildId() {
  return `child_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 从补贴计算器数据中导入孩子信息
 * @param {Array} calculatorChildren - 补贴计算器中的孩子数据
 * @returns {Array} 标准化的孩子数据
 */
function importFromCalculator(calculatorChildren) {
  if (!calculatorChildren || calculatorChildren.length === 0) {
    return []
  }
  
  const now = new Date().toISOString()
  
  return calculatorChildren
    .filter(child => child.birthDate) // 只导入有出生日期的孩子
    .map((child, index) => ({
      id: generateChildId(),
      name: child.name || `宝宝${index + 1}`,
      birthDate: child.birthDate,
      gender: child.gender || '',
      nickname: child.nickname || '',
      avatar: child.avatar || '',
      createTime: now,
      updateTime: now
    }))
}

/**
 * 获取所有孩子信息
 * @returns {Array} 孩子列表
 */
function getAllChildren() {
  try {
    return wx.getStorageSync('children_list') || []
  } catch (error) {
    console.error('获取孩子列表失败:', error)
    return []
  }
}

/**
 * 保存孩子列表
 * @param {Array} children - 孩子列表
 * @returns {boolean} 保存是否成功
 */
function saveChildren(children) {
  try {
    wx.setStorageSync('children_list', children)
    return true
  } catch (error) {
    console.error('保存孩子列表失败:', error)
    return false
  }
}

/**
 * 添加新孩子
 * @param {Object} childData - 孩子数据
 * @returns {Object|null} 添加的孩子数据或null
 */
function addChild(childData) {
  try {
    const children = getAllChildren()
    const now = new Date().toISOString()
    
    const newChild = Object.assign({}, ChildModel, childData, {
      id: generateChildId(),
      createTime: now,
      updateTime: now
    })
    
    children.push(newChild)
    
    if (saveChildren(children)) {
      return newChild
    }
    return null
  } catch (error) {
    console.error('添加孩子失败:', error)
    return null
  }
}

/**
 * 更新孩子信息
 * @param {string} childId - 孩子ID
 * @param {Object} updateData - 更新数据
 * @returns {boolean} 更新是否成功
 */
function updateChild(childId, updateData) {
  try {
    const children = getAllChildren()
    const childIndex = children.findIndex(child => child.id === childId)
    
    if (childIndex === -1) {
      return false
    }
    
    children[childIndex] = Object.assign({}, children[childIndex], updateData, {
      updateTime: new Date().toISOString()
    })
    
    return saveChildren(children)
  } catch (error) {
    console.error('更新孩子信息失败:', error)
    return false
  }
}

/**
 * 删除孩子
 * @param {string} childId - 孩子ID
 * @returns {boolean} 删除是否成功
 */
function deleteChild(childId) {
  try {
    const children = getAllChildren()
    const filteredChildren = children.filter(child => child.id !== childId)
    
    return saveChildren(filteredChildren)
  } catch (error) {
    console.error('删除孩子失败:', error)
    return false
  }
}

/**
 * 根据ID获取孩子信息
 * @param {string} childId - 孩子ID
 * @returns {Object|null} 孩子信息或null
 */
function getChildById(childId) {
  try {
    const children = getAllChildren()
    return children.find(child => child.id === childId) || null
  } catch (error) {
    console.error('获取孩子信息失败:', error)
    return null
  }
}

/**
 * 检查并同步补贴计算器数据
 * @returns {Array} 同步后的孩子列表
 */
function syncWithCalculator() {
  try {
    // 获取当前存储的孩子列表
    let children = getAllChildren()
    
    // 尝试从补贴计算器页面获取数据
    const calculatorData = wx.getStorageSync('calculator_children') || []
    
    if (calculatorData.length > 0) {
      // 检查是否有新的孩子数据需要导入
      const importedChildren = importFromCalculator(calculatorData)
      
      // 合并数据，避免重复
      importedChildren.forEach(importedChild => {
        const exists = children.some(child => 
          child.birthDate === importedChild.birthDate
        )
        
        if (!exists) {
          children.push(importedChild)
        }
      })
      
      // 保存更新后的列表
      saveChildren(children)
    }
    
    // 如果仍然没有孩子数据，创建一个默认的
    if (children.length === 0) {
      const defaultChild = {
        id: generateChildId(),
        name: '我的宝宝',
        birthDate: '',
        gender: '',
        nickname: '',
        avatar: '',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      
      children = [defaultChild]
      saveChildren(children)
    }
    
    return children
  } catch (error) {
    console.error('同步数据失败:', error)
    return []
  }
}

/**
 * 计算孩子年龄
 * @param {string} birthDate - 出生日期
 * @returns {Object} 年龄信息
 */
function calculateAge(birthDate) {
  if (!birthDate) {
    return { years: 0, months: 0, days: 0, totalMonths: 0 }
  }
  
  const birth = new Date(birthDate)
  const now = new Date()
  
  let years = now.getFullYear() - birth.getFullYear()
  let months = now.getMonth() - birth.getMonth()
  let days = now.getDate() - birth.getDate()
  
  if (days < 0) {
    months--
    const lastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
    days += lastMonth.getDate()
  }
  
  if (months < 0) {
    years--
    months += 12
  }
  
  const totalMonths = years * 12 + months
  
  return { years, months, days, totalMonths }
}

/**
 * 格式化年龄显示
 * @param {string} birthDate - 出生日期
 * @returns {string} 格式化的年龄字符串
 */
function formatAge(birthDate) {
  const age = calculateAge(birthDate)
  
  if (age.years > 0) {
    return `${age.years}岁${age.months}个月`
  } else if (age.months > 0) {
    return `${age.months}个月${age.days}天`
  } else {
    return `${age.days}天`
  }
}

/**
 * 验证孩子数据
 * @param {Object} childData - 孩子数据
 * @returns {Object} 验证结果
 */
function validateChild(childData) {
  const errors = []
  
  if (!childData.name || childData.name.trim() === '') {
    errors.push('请输入孩子姓名')
  }
  
  if (!childData.birthDate) {
    errors.push('请选择出生日期')
  } else {
    const birthDate = new Date(childData.birthDate)
    const now = new Date()
    
    if (birthDate > now) {
      errors.push('出生日期不能是未来日期')
    }
    
    const minDate = new Date('1990-01-01')
    if (birthDate < minDate) {
      errors.push('出生日期不能早于1990年1月1日')
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

module.exports = {
  ChildModel,
  generateChildId,
  importFromCalculator,
  getAllChildren,
  saveChildren,
  addChild,
  updateChild,
  deleteChild,
  getChildById,
  syncWithCalculator,
  calculateAge,
  formatAge,
  validateChild
}
