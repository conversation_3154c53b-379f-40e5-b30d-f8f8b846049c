// growth-diary.js - 成长日记主页面
const growthDiary = require('../../utils/growthDiary')
const childrenManager = require('../../utils/childrenManager')

Page({
  data: {
    currentChild: null, // 当前选中的孩子
    children: [], // 孩子列表
    diaries: [], // 成长日记列表
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    showChildSelector: false,
    loading: false,
    
    // 月份选择器数据
    years: [],
    months: [
      { value: 1, label: '1月' },
      { value: 2, label: '2月' },
      { value: 3, label: '3月' },
      { value: 4, label: '4月' },
      { value: 5, label: '5月' },
      { value: 6, label: '6月' },
      { value: 7, label: '7月' },
      { value: 8, label: '8月' },
      { value: 9, label: '9月' },
      { value: 10, label: '10月' },
      { value: 11, label: '11月' },
      { value: 12, label: '12月' }
    ],
    
    // 统计数据
    stats: {
      totalDiaries: 0,
      currentMonthDiary: null,
      recentActivity: []
    }
  },

  onLoad(options) {
    this.initializeData()
    this.loadChildren()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.currentChild) {
      this.loadDiaries()
    }
  },

  // 初始化数据
  initializeData() {
    const currentYear = new Date().getFullYear()
    const years = []
    
    // 生成年份列表（当前年份前后各3年）
    for (let i = currentYear - 3; i <= currentYear + 3; i++) {
      years.push({ value: i, label: `${i}年` })
    }
    
    this.setData({ years })
  },

  // 加载孩子列表
  loadChildren() {
    try {
      // 同步并获取孩子列表
      const children = childrenManager.syncWithCalculator()

      if (children.length === 0) {
        // 如果仍然没有孩子数据，显示添加孩子的提示
        this.setData({
          children: [],
          currentChild: null
        })
        this.showAddChildDialog()
        return
      }

      // 获取上次选择的孩子ID
      const lastSelectedChildId = wx.getStorageSync('last_selected_child_id')
      let currentChild = children[0] // 默认选择第一个

      if (lastSelectedChildId) {
        const foundChild = children.find(child => child.id === lastSelectedChildId)
        if (foundChild) {
          currentChild = foundChild
        }
      }

      this.setData({
        children,
        currentChild
      })

      this.loadDiaries()
    } catch (error) {
      console.error('加载孩子列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 加载成长日记列表
  loadDiaries() {
    if (!this.data.currentChild) return
    
    this.setData({ loading: true })
    
    try {
      const diaries = growthDiary.getGrowthDiaries(this.data.currentChild.id)
      const currentMonthDiary = growthDiary.getGrowthDiary(
        this.data.currentChild.id,
        this.data.currentYear,
        this.data.currentMonth
      )
      
      // 计算统计数据
      const stats = {
        totalDiaries: diaries.length,
        currentMonthDiary,
        recentActivity: diaries.slice(0, 3) // 最近3条记录
      }
      
      this.setData({
        diaries,
        stats,
        loading: false
      })
    } catch (error) {
      console.error('加载成长日记失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 切换孩子
  onChildChange(e) {
    const childIndex = e.detail.value
    const selectedChild = this.data.children[childIndex]

    // 检查选中的孩子是否有效
    if (!selectedChild || !selectedChild.id) {
      wx.showToast({
        title: '选择的孩子信息无效',
        icon: 'error'
      })
      return
    }

    // 保存选择的孩子ID
    wx.setStorageSync('last_selected_child_id', selectedChild.id)

    this.setData({
      currentChild: selectedChild,
      showChildSelector: false
    })

    this.loadDiaries()
  },

  // 显示孩子选择器
  showChildSelector() {
    this.setData({ showChildSelector: true })
  },

  // 隐藏孩子选择器
  hideChildSelector() {
    this.setData({ showChildSelector: false })
  },

  // 年份变化
  onYearChange(e) {
    const year = this.data.years[e.detail.value].value
    this.setData({ currentYear: year })
    this.loadDiaries()
  },

  // 月份变化
  onMonthChange(e) {
    const month = this.data.months[e.detail.value].value
    this.setData({ currentMonth: month })
    this.loadDiaries()
  },

  // 创建新日记
  createNewDiary() {
    if (!this.data.currentChild) {
      wx.showToast({
        title: '请先选择孩子',
        icon: 'error'
      })
      return
    }
    
    const url = `/pages/diary-edit/diary-edit?childId=${this.data.currentChild.id}&year=${this.data.currentYear}&month=${this.data.currentMonth}&mode=create`
    
    wx.navigateTo({ url })
  },

  // 查看日记详情
  viewDiary(e) {
    const dataset = e.currentTarget.dataset
    const year = dataset.year
    const month = dataset.month
    const url = `/pages/diary-detail/diary-detail?childId=${this.data.currentChild.id}&year=${year}&month=${month}`

    wx.navigateTo({ url })
  },

  // 编辑日记
  editDiary(e) {
    const dataset = e.currentTarget.dataset
    const year = dataset.year
    const month = dataset.month
    const url = `/pages/diary-edit/diary-edit?childId=${this.data.currentChild.id}&year=${year}&month=${month}&mode=edit`

    wx.navigateTo({ url })
  },

  // 删除日记
  deleteDiary(e) {
    const dataset = e.currentTarget.dataset
    const year = dataset.year
    const month = dataset.month
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除${year}年${month}月的成长日记吗？`,
      success: (res) => {
        if (res.confirm) {
          const success = growthDiary.deleteGrowthDiary(
            this.data.currentChild.id,
            year,
            month
          )
          
          if (success) {
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            this.loadDiaries()
          } else {
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDiaries()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '成长日记 - 记录宝宝每一个珍贵时刻',
      path: '/pages/growth-diary/growth-diary'
    }
  },

  onShareTimeline() {
    return {
      title: '成长日记 - 记录宝宝每一个珍贵时刻'
    }
  },

  // 显示添加孩子对话框
  showAddChildDialog() {
    wx.showModal({
      title: '添加宝宝',
      content: '还没有宝宝信息，是否添加第一个宝宝？',
      confirmText: '添加',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          this.navigateToAddChild()
        }
      }
    })
  },

  // 跳转到添加孩子页面
  navigateToAddChild() {
    wx.navigateTo({
      url: '/pages/child-manage/child-manage?mode=add'
    })
  },

  // 管理孩子信息
  manageChildren() {
    wx.navigateTo({
      url: '/pages/child-manage/child-manage?mode=manage'
    })
  },

  // 编辑当前孩子信息
  editCurrentChild() {
    if (!this.data.currentChild) {
      wx.showToast({
        title: '请先选择孩子',
        icon: 'error'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/child-manage/child-manage?mode=edit&childId=${this.data.currentChild.id}`
    })
  },

  // 获取孩子年龄显示
  getChildAgeText(birthDate) {
    if (!birthDate) return '未设置出生日期'
    return childrenManager.formatAge(birthDate)
  }
})
