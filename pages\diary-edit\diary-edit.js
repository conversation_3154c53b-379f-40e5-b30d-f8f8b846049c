// diary-edit.js - 成长日记编辑页面
const growthDiary = require('../../utils/growthDiary')

Page({
  data: {
    childId: '',
    year: 0,
    month: 0,
    mode: 'create', // create 或 edit
    diary: null,
    loading: false,
    saving: false,
    
    // 表单数据
    formData: {
      careRequirements: {
        skinCare: '',
        oralCare: '',
        bathingCare: '',
        sleepCare: '',
        exerciseCare: '',
        emotionalCare: '',
        notes: ''
      },
      precautions: {
        safety: [],
        health: [],
        development: [],
        environment: [],
        interaction: [],
        customNotes: ''
      },
      feeding: {
        breastfeeding: {
          frequency: 0,
          duration: 0,
          notes: ''
        },
        formulaFeeding: {
          frequency: 0,
          amount: 0,
          brand: '',
          notes: ''
        },
        complementaryFood: {
          introduced: false,
          foods: [],
          allergies: [],
          schedule: '',
          notes: ''
        },
        water: {
          amount: 0,
          notes: ''
        }
      },
      urination: {
        frequency: 0,
        color: '',
        smell: '',
        diaperChanges: 0,
        accidents: 0,
        notes: ''
      },
      defecation: {
        frequency: 0,
        consistency: '',
        color: '',
        smell: '',
        difficulty: false,
        blood: false,
        notes: ''
      },
      clothing: {
        sizes: {
          tops: '',
          bottoms: '',
          shoes: '',
          hats: '',
          socks: ''
        },
        materials: [],
        specialNeeds: '',
        notes: ''
      },
      health: {
        weight: 0,
        height: 0,
        headCircumference: 0,
        temperature: 0,
        notes: ''
      },
      sleep: {
        nightSleep: {
          bedtime: '',
          wakeupTime: '',
          duration: 0,
          nightWakings: 0,
          quality: ''
        },
        napSleep: {
          frequency: 0,
          totalDuration: 0
        },
        notes: ''
      },
      parentNotes: {
        observations: '',
        concerns: '',
        achievements: '',
        challenges: '',
        tips: '',
        questions: '',
        mood: '',
        gratitude: ''
      }
    },
    
    // 选择器数据
    colorOptions: ['淡黄', '深黄', '无色', '其他'],
    smellOptions: ['正常', '异常'],
    consistencyOptions: ['软便', '成形', '硬便', '水样'],
    qualityOptions: ['好', '一般', '差']
  },

  onLoad(options) {
    const childId = options.childId
    const year = options.year
    const month = options.month
    const mode = options.mode || 'create'
    
    if (!childId || !year || !month) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }
    
    this.setData({
      childId,
      year: parseInt(year),
      month: parseInt(month),
      mode
    })
    
    if (mode === 'edit') {
      this.loadDiary()
    } else {
      this.initializeNewDiary()
    }
  },

  // 加载现有日记
  loadDiary() {
    this.setData({ loading: true })
    
    try {
      const diary = growthDiary.getGrowthDiary(
        this.data.childId,
        this.data.year,
        this.data.month
      )
      
      if (!diary) {
        wx.showToast({
          title: '日记不存在',
          icon: 'error'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
        return
      }
      
      this.setData({
        diary,
        formData: Object.assign({}, diary),
        loading: false
      })
    } catch (error) {
      console.error('加载日记失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 初始化新日记
  initializeNewDiary() {
    const newDiary = growthDiary.createGrowthDiary(
      this.data.childId,
      this.data.year,
      this.data.month
    )
    
    this.setData({
      diary: newDiary,
      formData: Object.assign({}, newDiary)
    })
  },

  // 输入框变化处理
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value

    const updateData = {}
    updateData['formData.' + field] = value

    this.setData(updateData)
  },

  // 数字输入框变化处理
  onNumberChange(e) {
    const field = e.currentTarget.dataset.field
    const value = parseFloat(e.detail.value) || 0

    const updateData = {}
    updateData['formData.' + field] = value

    this.setData(updateData)
  },

  // 选择器变化处理
  onPickerChange(e) {
    const dataset = e.currentTarget.dataset
    const field = dataset.field
    const options = dataset.options
    const index = e.detail.value
    const value = this.data[options][index]
    
    const updateData = {}
    updateData['formData.' + field] = value

    this.setData(updateData)
  },

  // 开关变化处理
  onSwitchChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value

    const updateData = {}
    updateData['formData.' + field] = value

    this.setData(updateData)
  },

  // 添加列表项
  addListItem(e) {
    const field = e.currentTarget.dataset.field
    
    wx.showModal({
      title: '添加项目',
      editable: true,
      placeholderText: '请输入内容',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          const currentList = this.data.formData[field] || []
          const newList = currentList.slice()
          newList.push(res.content.trim())

          const updateData = {}
          updateData['formData.' + field] = newList

          this.setData(updateData)
        }
      }
    })
  },

  // 删除列表项
  removeListItem(e) {
    const dataset = e.currentTarget.dataset
    const field = dataset.field
    const index = dataset.index
    const currentList = this.data.formData[field] || []
    const newList = currentList.filter(function(_, i) { return i !== parseInt(index) })
    
    const updateData = {}
    updateData['formData.' + field] = newList

    this.setData(updateData)
  },

  // 保存日记
  saveDiary() {
    if (this.data.saving) return
    
    this.setData({ saving: true })
    
    try {
      // 验证数据
      const validation = growthDiary.validateGrowthDiary(this.data.formData)
      if (!validation.valid) {
        wx.showToast({
          title: validation.errors[0],
          icon: 'error'
        })
        this.setData({ saving: false })
        return
      }
      
      // 保存数据
      const success = growthDiary.saveGrowthDiary(this.data.formData)
      
      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('保存日记失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
    
    this.setData({ saving: false })
  },

  // 取消编辑
  cancelEdit() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消编辑吗？未保存的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  }
})
