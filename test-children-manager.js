// 孩子管理功能测试文件

// 模拟微信小程序环境
const mockWx = {
  getStorageSync: function(key) {
    const storage = this._storage || {}
    return storage[key] || null
  },
  setStorageSync: function(key, data) {
    this._storage = this._storage || {}
    this._storage[key] = data
  },
  _storage: {}
}

// 模拟 getApp 函数
global.getApp = function() {
  return {
    globalData: {
      policyConfig: {
        monthlyAmount: 300,
        maxAge: 3,
        policyStartDate: '2025-01-01',
        minBirthDate: '1990-01-01'
      }
    }
  }
}

// 模拟 wx 对象
global.wx = mockWx

// 引入孩子管理模块
const childrenManager = require('./utils/childrenManager')

// 测试函数
function runTests() {
  console.log('开始测试孩子管理功能...\n')
  
  // 清空存储
  mockWx._storage = {}
  
  // 测试1: 添加第一个孩子
  console.log('测试1: 添加第一个孩子')
  const childData1 = {
    name: '小明',
    birthDate: '2023-06-15',
    gender: '男',
    nickname: '明明'
  }
  
  const newChild1 = childrenManager.addChild(childData1)
  if (newChild1) {
    console.log('✓ 成功添加孩子:', newChild1.name)
    console.log('✓ 孩子ID:', newChild1.id)
  } else {
    console.log('✗ 添加孩子失败')
  }
  
  // 测试2: 添加第二个孩子
  console.log('\n测试2: 添加第二个孩子')
  const childData2 = {
    name: '小红',
    birthDate: '2024-03-20',
    gender: '女',
    nickname: '红红'
  }
  
  const newChild2 = childrenManager.addChild(childData2)
  if (newChild2) {
    console.log('✓ 成功添加孩子:', newChild2.name)
  }
  
  // 测试3: 获取所有孩子
  console.log('\n测试3: 获取所有孩子')
  const allChildren = childrenManager.getAllChildren()
  console.log('✓ 获取到', allChildren.length, '个孩子')
  allChildren.forEach((child, index) => {
    console.log(`  ${index + 1}. ${child.name} (${child.gender}) - ${child.birthDate}`)
  })
  
  // 测试4: 计算年龄
  console.log('\n测试4: 计算年龄')
  allChildren.forEach(child => {
    const age = childrenManager.calculateAge(child.birthDate)
    const ageText = childrenManager.formatAge(child.birthDate)
    console.log(`✓ ${child.name}: ${ageText} (${age.totalMonths}个月)`)
  })
  
  // 测试5: 更新孩子信息
  console.log('\n测试5: 更新孩子信息')
  const updateData = {
    nickname: '小明明',
    gender: '男孩'
  }
  
  const updateResult = childrenManager.updateChild(newChild1.id, updateData)
  if (updateResult) {
    console.log('✓ 成功更新孩子信息')
    const updatedChild = childrenManager.getChildById(newChild1.id)
    console.log('✓ 新昵称:', updatedChild.nickname)
  }
  
  // 测试6: 验证数据
  console.log('\n测试6: 验证数据')
  const validData = {
    name: '测试宝宝',
    birthDate: '2024-01-01',
    gender: '女'
  }
  
  const invalidData = {
    name: '',
    birthDate: '2030-01-01', // 未来日期
    gender: '女'
  }
  
  const validResult = childrenManager.validateChild(validData)
  const invalidResult = childrenManager.validateChild(invalidData)
  
  console.log('✓ 有效数据验证:', validResult.valid ? '通过' : '失败')
  console.log('✓ 无效数据验证:', invalidResult.valid ? '通过' : '失败')
  if (!invalidResult.valid) {
    console.log('  错误信息:', invalidResult.errors.join(', '))
  }
  
  // 测试7: 模拟从补贴计算器导入数据
  console.log('\n测试7: 从补贴计算器导入数据')
  const calculatorData = [
    { index: 1, birthDate: '2023-12-01' },
    { index: 2, birthDate: '2022-08-15' },
    { index: 3, birthDate: '' } // 无出生日期，应该被过滤
  ]
  
  const importedChildren = childrenManager.importFromCalculator(calculatorData)
  console.log('✓ 导入了', importedChildren.length, '个孩子')
  importedChildren.forEach((child, index) => {
    console.log(`  ${index + 1}. ${child.name} - ${child.birthDate}`)
  })
  
  // 测试8: 同步功能
  console.log('\n测试8: 同步功能')
  // 模拟补贴计算器数据
  mockWx.setStorageSync('calculator_children', calculatorData)
  
  const syncedChildren = childrenManager.syncWithCalculator()
  console.log('✓ 同步后共有', syncedChildren.length, '个孩子')
  
  // 测试9: 删除孩子
  console.log('\n测试9: 删除孩子')
  const deleteResult = childrenManager.deleteChild(newChild2.id)
  if (deleteResult) {
    console.log('✓ 成功删除孩子')
    const remainingChildren = childrenManager.getAllChildren()
    console.log('✓ 剩余', remainingChildren.length, '个孩子')
  }
  
  console.log('\n所有测试完成！')
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = { runTests }
} else {
  // 浏览器环境
  runTests()
}

// 如果直接运行此文件
if (require.main === module) {
  runTests()
}
