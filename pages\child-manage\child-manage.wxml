<!--child-manage.wxml - 孩子管理页面-->
<wxs module="utils">
  function formatAge(birthDate) {
    if (!birthDate) return '未设置出生日期'

    var birth = getDate(birthDate)
    var now = getDate()

    var years = now.getFullYear() - birth.getFullYear()
    var months = now.getMonth() - birth.getMonth()
    var days = now.getDate() - birth.getDate()

    if (days < 0) {
      months--
      var lastMonth = getDate(now.getFullYear(), now.getMonth(), 0)
      days += lastMonth.getDate()
    }

    if (months < 0) {
      years--
      months += 12
    }

    if (years > 0) {
      return years + '岁' + months + '个月'
    } else if (months > 0) {
      return months + '个月' + days + '天'
    } else {
      return days + '天'
    }
  }

  module.exports = {
    formatAge: formatAge
  }
</wxs>

<view class="container">
  <!-- 添加/编辑模式 -->
  <view wx:if="{{mode === 'add' || mode === 'edit'}}" class="form-container">
    <!-- 头部 -->
    <view class="header">
      <view class="card">
        <view class="card-body">
          <view class="card-title">
            <text class="icon">👶</text>
            <text>{{mode === 'add' ? '添加宝宝' : '编辑宝宝信息'}}</text>
          </view>
          <text class="subtitle">请填写宝宝的基本信息</text>
        </view>
      </view>
    </view>

    <!-- 表单 -->
    <view class="form-section">
      <view class="card">
        <view class="card-body">
          <!-- 姓名 -->
          <view class="form-group">
            <text class="form-label">宝宝姓名 *</text>
            <input class="form-input {{errors.name ? 'error' : ''}}" 
                   placeholder="请输入宝宝的姓名"
                   value="{{formData.name}}"
                   data-field="name"
                   bindinput="onInputChange" />
            <text wx:if="{{errors.name}}" class="error-text">{{errors.name}}</text>
          </view>

          <!-- 出生日期 -->
          <view class="form-group">
            <text class="form-label">出生日期 *</text>
            <picker mode="date"
                    value="{{formData.birthDate}}"
                    bindchange="onBirthDateChange"
                    end="{{maxDate}}">
              <view class="picker-input {{errors.birthDate ? 'error' : ''}}">
                <text wx:if="{{formData.birthDate}}">{{formData.birthDate}}</text>
                <text wx:else class="placeholder">请选择出生日期</text>
              </view>
            </picker>
            <text wx:if="{{errors.birthDate}}" class="error-text">{{errors.birthDate}}</text>
          </view>

          <!-- 性别 -->
          <view class="form-group">
            <text class="form-label">性别</text>
            <picker range="{{genderOptions}}" 
                    value="{{formData.gender}}"
                    bindchange="onGenderChange">
              <view class="picker-input">
                <text wx:if="{{formData.gender}}">{{formData.gender}}</text>
                <text wx:else class="placeholder">请选择性别</text>
              </view>
            </picker>
          </view>

          <!-- 昵称 -->
          <view class="form-group">
            <text class="form-label">昵称</text>
            <input class="form-input" 
                   placeholder="请输入宝宝的昵称（可选）"
                   value="{{formData.nickname}}"
                   data-field="nickname"
                   bindinput="onInputChange" />
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <button class="btn-secondary" bindtap="cancel">取消</button>
      <button class="btn-primary" bindtap="saveChild" disabled="{{saving}}">
        <text wx:if="{{saving}}">保存中...</text>
        <text wx:else>{{mode === 'add' ? '添加' : '保存'}}</text>
      </button>
    </view>
  </view>

  <!-- 管理模式 -->
  <view wx:if="{{mode === 'manage'}}" class="manage-container">
    <!-- 头部 -->
    <view class="header">
      <view class="card">
        <view class="card-body">
          <view class="header-content">
            <view class="title-section">
              <view class="card-title">
                <text class="icon">👨‍👩‍👧‍👦</text>
                <text>管理宝宝</text>
              </view>
              <text class="subtitle">管理所有宝宝的信息</text>
            </view>
            <button class="btn-primary" bindtap="navigateToAddChild">
              <text class="icon">➕</text>
              <text>添加</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 孩子列表 -->
    <view class="children-list">
      <view wx:if="{{children.length > 0}}">
        <view wx:for="{{children}}" wx:key="id" class="child-card">
          <view class="card">
            <view class="card-body">
              <view class="child-info">
                <view class="child-avatar">
                  <text class="avatar-icon">👶</text>
                </view>
                <view class="child-details">
                  <text class="child-name">{{item.name}}</text>
                  <text class="child-nickname" wx:if="{{item.nickname}}">昵称：{{item.nickname}}</text>
                  <text class="child-birth">出生日期：{{item.birthDate || '未设置'}}</text>
                  <text class="child-age">年龄：{{utils.formatAge(item.birthDate)}}</text>
                  <text class="child-gender" wx:if="{{item.gender}}">性别：{{item.gender}}</text>
                </view>
                <view class="child-actions">
                  <button class="btn-text" bindtap="editChild" data-child-id="{{item.id}}">编辑</button>
                  <button class="btn-text btn-danger" bindtap="deleteChild" data-child-id="{{item.id}}">删除</button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view wx:else class="empty-state">
        <text class="empty-icon">👶</text>
        <text class="empty-text">还没有添加宝宝信息</text>
        <button class="btn-primary" bindtap="navigateToAddChild">添加第一个宝宝</button>
      </view>
    </view>
  </view>
</view>
