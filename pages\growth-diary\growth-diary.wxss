/* growth-diary.wxss - 成长日记主页面样式 */

/* 引入全局样式变量 */
@import "../../app.wxss";

/* 头部样式 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.child-selector {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: var(--bg-light);
  border-radius: 12rpx;
  border: 1px solid var(--border-light);
  min-width: 200rpx;
  justify-content: space-between;
}

.child-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.child-name {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.child-age {
  font-size: 22rpx;
  color: var(--text-secondary);
  margin-top: 4rpx;
}

.add-child-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.icon-arrow {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-left: 12rpx;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  color: white;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 月份选择器 */
.month-selector {
  margin-bottom: 32rpx;
}

.selector-row {
  display: flex;
  justify-content: space-between;
  gap: 32rpx;
}

.selector-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.picker-value {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 600;
  padding: 12rpx 24rpx;
  background: var(--bg-light);
  border-radius: 8rpx;
  border: 1px solid var(--border-light);
  min-width: 120rpx;
  text-align: center;
}

/* 当前日记 */
.current-diary {
  margin-bottom: 32rpx;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-secondary {
  background: var(--bg-light);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.diary-preview {
  margin-top: 16rpx;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: var(--bg-light);
  border-radius: 12rpx;
  border: 1px solid var(--border-light);
}

.preview-content {
  flex: 1;
}

.preview-title {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.preview-time {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 历史记录 */
.diary-list {
  margin-top: 16rpx;
}

.diary-item {
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
}

.diary-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.diary-info {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.diary-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.month {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.year {
  font-size: 20rpx;
  color: white;
  opacity: 0.9;
  margin-top: 4rpx;
}

.diary-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.diary-title {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.diary-time {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.diary-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 16rpx;
  padding-left: 144rpx;
}

.btn-text {
  background: none;
  border: none;
  font-size: 26rpx;
  color: var(--primary-color);
  padding: 8rpx 16rpx;
}

.btn-danger {
  color: #ef4444 !important;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 48rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.loading-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 36rpx;
  color: var(--text-secondary);
  padding: 0;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
}

.children-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.child-option {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

.child-option:last-child {
  border-bottom: none;
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-icon {
  font-size: 40rpx;
}

.child-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.child-birth, .child-age {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.child-selected {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1px solid var(--border-light);
}
