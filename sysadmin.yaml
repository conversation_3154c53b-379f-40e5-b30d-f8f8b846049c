proxies:
  - name: Tokyo_1_x2
    type: hysteria
    server: aisbest.zone.id
    port: 8445
    auth-str: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    protocol: udp
    up: 100
    down: 100
    skip-cert-verify: true
    fast-open: true
  - name: Seoul_2_x3
    type: vmess
    server: f2.aaixiaomi.dpdns.org
    port: 443
    uuid: 8c9a6f0d-bcdf-59f5-8b11-fb8d65686bc8
    alterId: 0
    cipher: auto
    udp: true
    tls: true
    client-fingerprint: qq
    network: tcp
  - name: Seoul_1_x3
    type: hysteria
    server: aiisbest.zone.id
    port: 8741
    auth-str: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    obfs: "6463@#@E"
    protocol: udp
    up: 100
    down: 100
    skip-cert-verify: true
    fast-open: true
  - name: Seoul_1_x2
    type: hysteria
    server: aiisbest.zone.id
    port: 8659
    auth-str: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    protocol: udp
    up: 100
    down: 100
    skip-cert-verify: true
    fast-open: true
  - name: Seoul_1_x1
    type: vmess
    server: aiisbest.zone.id
    port: 8132
    uuid: 8c9a6f0d-bcdf-59f5-8b11-fb8d65686bc8
    alterId: 0
    cipher: auto
    udp: true
    tls: true
    client-fingerprint: ios
    skip-cert-verify: true
    network: tcp
  - name: Seoul_2_x2
    type: hysteria
    server: f2.aaixiaomi.dpdns.org
    port: 8431
    auth-str: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    obfs: 87sdsdsa
    protocol: udp
    up: 100
    down: 100
    skip-cert-verify: true
    fast-open: true
  - name: Seoul_2_x1
    type: ss
    server: ***************
    port: 8033
    cipher: xchacha20-ietf-poly1305
    password: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    udp: true
  - name: Tokyo_1_x3
    type: hysteria
    server: aisbest.zone.id
    port: 8443
    auth-str: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    obfs: "88244"
    protocol: wechat-video
    up: 100
    down: 100
    skip-cert-verify: true
    fast-open: true
  - name: Tokyo_1
    type: vmess
    server: aisbest.zone.id
    port: 8444
    uuid: 8c9a6f0d-bcdf-59f5-8b11-fb8d65686bc8
    alterId: 0
    cipher: auto
    udp: true
    tls: true
    client-fingerprint: ios
    skip-cert-verify: true
    network: tcp
  - name: Seoul_x1
    type: hysteria
    server: f.aaixiaomi.dpdns.org
    port: 8878
    auth-str: 02SVLzL3NDYSRzBfsW_DLG0PTNXJIqUFUBGfdBt6tCrWMyzL
    protocol: udp
    up: 100
    down: 100
    skip-cert-verify: true
    fast-open: true
  - name: Seoul_x2
    type: vless
    server: f.aaixiaomi.dpdns.org
    port: 8444
    uuid: 8c9a6f0d-bcdf-59f5-8b11-fb8d65686bc8
    network: ws
    tls: true
    udp: true
    flow: none
    client-fingerprint: chrome
    servername: ""
    skip-cert-verify: true
    ws-opts:
      path: /trojan-panel-websocket-path
      headers:
        Host: ""
  - name: Seoul_x3
    type: vmess
    server: f.aaixiaomi.dpdns.org
    port: 8864
    uuid: 8c9a6f0d-bcdf-59f5-8b11-fb8d65686bc8
    alterId: 0
    cipher: auto
    udp: true
    tls: true
    client-fingerprint: chrome
    skip-cert-verify: true
    network: ws

proxy-groups:
  - name: PROXY
    type: select
    proxies:
      - Tokyo_1_x2
      - Seoul_2_x3
      - Seoul_1_x3
      - Seoul_1_x2
      - Seoul_1_x1
      - Seoul_2_x2
      - Seoul_2_x1
      - Tokyo_1_x3
      - Tokyo_1
      - Seoul_x1
      - Seoul_x2
      - Seoul_x3

rules:
  - RULE-SET,applications,DIRECT
  - DOMAIN,clash.razord.top,DIRECT
  - DOMAIN,yacd.haishan.me,DIRECT
  - RULE-SET,private,DIRECT
  - RULE-SET,reject,REJECT
  - RULE-SET,icloud,DIRECT
  - RULE-SET,apple,DIRECT
  - RULE-SET,google,DIRECT
  - RULE-SET,proxy,PROXY
  - RULE-SET,direct,DIRECT
  - RULE-SET,lancidr,DIRECT
  - RULE-SET,cncidr,DIRECT
  - RULE-SET,telegramcidr,PROXY
  - GEOIP,LAN,DIRECT
  - GEOIP,CN,DIRECT
  - MATCH,PROXY

rule-providers:
  reject:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt"
    path: ./ruleset/reject.yaml
    interval: 86400
  icloud:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt"
    path: ./ruleset/icloud.yaml
    interval: 86400
  apple:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt"
    path: ./ruleset/apple.yaml
    interval: 86400
  google:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt"
    path: ./ruleset/google.yaml
    interval: 86400
  proxy:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt"
    path: ./ruleset/proxy.yaml
    interval: 86400
  direct:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt"
    path: ./ruleset/direct.yaml
    interval: 86400
  private:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt"
    path: ./ruleset/private.yaml
    interval: 86400
  gfw:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt"
    path: ./ruleset/gfw.yaml
    interval: 86400
  greatfire:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/greatfire.txt"
    path: ./ruleset/greatfire.yaml
    interval: 86400
  tld-not-cn:
    type: http
    behavior: domain
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt"
    path: ./ruleset/tld-not-cn.yaml
    interval: 86400
  telegramcidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt"
    path: ./ruleset/telegramcidr.yaml
    interval: 86400
  cncidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt"
    path: ./ruleset/cncidr.yaml
    interval: 86400
  lancidr:
    type: http
    behavior: ipcidr
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt"
    path: ./ruleset/lancidr.yaml
    interval: 86400
  applications:
    type: http
    behavior: classical
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt"
    path: ./ruleset/applications.yaml
    interval: 86400
