<!--encyclopedia-diary.wxml - 百科日记主页面-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body">
        <view class="card-title">
          <text class="icon">📚</text>
          <text>育儿百科日记</text>
        </view>
        <text class="subtitle">按月龄提供专业的育儿指导和知识</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input class="search-input" 
               placeholder="搜索育儿知识..." 
               value="{{searchQuery}}"
               bindinput="onSearchInput" />
        <view class="search-icon">🔍</view>
        <view wx:if="{{searchQuery}}" class="clear-icon" bindtap="clearSearch">×</view>
      </view>
    </view>
    
    <!-- 热门搜索 -->
    <view wx:if="{{!showSearchResults && searchQuery === ''}}" class="hot-searches">
      <text class="hot-title">热门搜索：</text>
      <view class="hot-tags">
        <text wx:for="{{hotSearches}}" wx:key="index" 
              class="hot-tag" 
              bindtap="onHotSearchTap"
              data-keyword="{{item}}">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view wx:if="{{showSearchResults}}" class="search-results">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🔍</text>
          <text>搜索结果</text>
        </view>
      </view>
      <view class="card-body">
        <view wx:if="{{searchResults.length > 0}}">
          <view wx:for="{{searchResults}}" wx:key="monthAge" 
                class="search-result-item"
                bindtap="onSearchResultTap"
                data-month-age="{{item.monthAge}}">
            <view class="result-title">{{item.title}}</view>
            <view class="result-matches">
              <view wx:for="{{item.matches}}" wx:key="index" wx:for-item="match" class="match-item">
                <text class="match-type">{{match.type === 'faq' ? 'FAQ' : '标题'}}</text>
                <text class="match-content">{{match.content}}</text>
              </view>
            </view>
          </view>
        </view>
        <view wx:else class="no-results">
          <text class="no-results-icon">🔍</text>
          <text class="no-results-text">未找到相关内容</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:if="{{!showSearchResults}}" class="main-content">
    <!-- 快速导航 -->
    <view class="quick-nav">
      <view class="card">
        <view class="card-header">
          <view class="card-title">
            <text class="icon">🚀</text>
            <text>快速导航</text>
          </view>
        </view>
        <view class="card-body">
          <view class="nav-grid">
            <view wx:for="{{quickNav}}" wx:key="id" 
                  class="nav-item"
                  bindtap="onQuickNavTap"
                  data-id="{{item.id}}"
                  data-month-age="{{item.monthAge}}">
              <text class="nav-icon">{{item.icon}}</text>
              <text class="nav-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 月龄分组选择 -->
    <view class="age-groups">
      <view class="card">
        <view class="card-header">
          <view class="card-title">
            <text class="icon">📅</text>
            <text>按年龄段浏览</text>
          </view>
        </view>
        <view class="card-body">
          <view class="group-tabs">
            <view wx:for="{{monthAgeGroups}}" wx:key="id" 
                  class="group-tab {{selectedGroup === item.id ? 'active' : ''}}"
                  bindtap="onGroupChange"
                  data-group-id="{{item.id}}">
              <text class="group-icon">{{item.icon}}</text>
              <text class="group-name">{{item.name}}</text>
              <text class="group-range">{{item.range[0]}}-{{item.range[1]}}月</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 具体月龄选择 -->
    <view class="month-selector">
      <view class="card">
        <view class="card-header">
          <view class="card-title">
            <text class="icon">📊</text>
            <text>选择具体月龄</text>
          </view>
        </view>
        <view class="card-body">
          <view class="month-picker-container">
            <picker range="{{monthAgeOptions}}" range-key="title" value="{{selectedMonthIndex}}" bindchange="onMonthPickerChange">
              <view class="month-picker">
                <text class="picker-text">{{currentMonthTitle}}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>


        </view>
      </view>
    </view>

    <!-- 当前百科日记内容 -->
    <view wx:if="{{currentDiary && !loading}}" class="diary-content">
      <view class="card">
        <view class="card-header">
          <view class="card-title">
            <text class="icon">📖</text>
            <text>{{currentDiary.title}}</text>
          </view>
          <view class="header-actions">
            <button class="btn-icon" bindtap="toggleFavorite" data-month-age="{{currentDiary.monthAge}}">
              <text class="icon">⭐</text>
            </button>
            <button class="btn-primary" bindtap="viewDetail" data-month-age="{{currentDiary.monthAge}}">
              <text>查看详情</text>
            </button>
          </view>
        </view>
        <view class="card-body">
          <!-- 呵护要求概览 -->
          <view id="section-feeding" class="content-section">
            <view class="section-title">
              <text class="icon">🍼</text>
              <text>喂养指导</text>
            </view>
            <view class="section-content">
              <view wx:if="{{currentDiary.feeding.breastfeeding.frequency}}" class="info-item">
                <text class="info-label">母乳喂养：</text>
                <text class="info-value">{{currentDiary.feeding.breastfeeding.frequency}}</text>
              </view>
              <view wx:if="{{currentDiary.feeding.formulaFeeding.frequency}}" class="info-item">
                <text class="info-label">配方奶：</text>
                <text class="info-value">{{currentDiary.feeding.formulaFeeding.frequency}}</text>
              </view>
            </view>
          </view>

          <!-- 睡眠指导 -->
          <view id="section-sleep" class="content-section">
            <view class="section-title">
              <text class="icon">😴</text>
              <text>睡眠指导</text>
            </view>
            <view class="section-content">
              <view wx:if="{{currentDiary.careRequirements.sleepCare}}" class="info-item">
                <text class="info-value">{{currentDiary.careRequirements.sleepCare}}</text>
              </view>
            </view>
          </view>

          <!-- 发育里程碑 -->
          <view id="section-development" class="content-section">
            <view class="section-title">
              <text class="icon">📈</text>
              <text>发育里程碑</text>
            </view>
            <view class="section-content">
              <view wx:if="{{currentDiary.milestones.physical.length > 0}}" class="milestone-category">
                <text class="milestone-title">身体发育：</text>
                <view wx:for="{{currentDiary.milestones.physical}}" wx:key="index" class="milestone-item">
                  <text>• {{item}}</text>
                </view>
              </view>
              <view wx:if="{{currentDiary.milestones.cognitive.length > 0}}" class="milestone-category">
                <text class="milestone-title">认知发育：</text>
                <view wx:for="{{currentDiary.milestones.cognitive}}" wx:key="index" class="milestone-item">
                  <text>• {{item}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 健康指标 -->
          <view id="section-health" class="content-section">
            <view class="section-title">
              <text class="icon">🏥</text>
              <text>健康指标</text>
            </view>
            <view class="section-content">
              <view wx:if="{{currentDiary.health}}" class="info-item">
                <text class="info-value">健康监测要点和指标说明</text>
              </view>
            </view>
          </view>

          <!-- 安全注意 -->
          <view id="section-safety" class="content-section">
            <view class="section-title">
              <text class="icon">🛡️</text>
              <text>安全注意</text>
            </view>
            <view class="section-content">
              <view wx:if="{{currentDiary.precautions.safety.length > 0}}" class="safety-list">
                <view wx:for="{{currentDiary.precautions.safety}}" wx:key="index" class="safety-item">
                  <text>• {{item}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 活动建议 -->
          <view id="section-activities" class="content-section">
            <view class="section-title">
              <text class="icon">🎯</text>
              <text>活动建议</text>
            </view>
            <view class="section-content">
              <view wx:if="{{currentDiary.careRequirements.exerciseCare}}" class="info-item">
                <text class="info-value">{{currentDiary.careRequirements.exerciseCare}}</text>
              </view>
            </view>
          </view>

          <!-- 常见问题 -->
          <view wx:if="{{currentDiary.faqs.length > 0}}" class="content-section">
            <view class="section-title">
              <text class="icon">❓</text>
              <text>常见问题</text>
            </view>
            <view class="section-content">
              <view wx:for="{{currentDiary.faqs}}" wx:key="index" class="faq-item">
                <view class="faq-question">{{item.question}}</view>
                <view class="faq-answer">{{item.answer}}</view>
                <view wx:if="{{item.tips}}" class="faq-tips">💡 {{item.tips}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text class="loading-icon">⏳</text>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!currentDiary && !loading}}" class="empty-state">
      <text class="empty-icon">📚</text>
      <text class="empty-text">暂无该月龄的百科数据</text>
    </view>
  </view>
</view>
