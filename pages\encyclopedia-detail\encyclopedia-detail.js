// encyclopedia-detail.js - 百科日记详情页面
const encyclopediaDiary = require('../../utils/encyclopediaDiary')

Page({
  data: {
    monthAge: 0,
    diary: null,
    loading: true,
    isFavorited: false,
    sectionCount: 5, // 实际章节数量

    // 展开状态控制
    expandedSections: {
      careRequirements: false,
      precautions: false,
      feeding: false,
      urination: false,
      defecation: false,
      clothing: false,
      milestones: false,
      health: false,
      sleep: false,
      activities: false,
      faqs: false,
      expertTips: false
    }
  },

  onLoad(options) {
    console.log('encyclopedia-detail onLoad options:', options)

    // 提供默认值以便测试
    const monthAge = options.monthAge || 1

    console.log('Parameters:', { monthAge })

    this.setData({ monthAge: parseInt(monthAge) })
    this.loadDiary()
  },

  // 加载百科日记数据
  loadDiary() {
    this.setData({ loading: true })

    try {
      const diary = encyclopediaDiary.getEncyclopediaDiary(this.data.monthAge)

      if (!diary) {
        // 提供默认数据以便测试
        const defaultDiary = {
          title: '新生儿期（0-1个月）',
          description: '专业的育儿指导和建议',
          careRequirements: {
            skinCare: '保持皮肤清洁干燥',
            oralCare: '用温水清洁口腔',
            bathingCare: '每天洗澡，水温37-40度',
            sleepCare: '保证充足睡眠，每天16-20小时'
          },
          precautions: {
            safety: ['避免摇晃婴儿', '注意保暖'],
            health: ['观察体温变化', '注意喂养量']
          },
          feeding: {
            breastfeeding: { frequency: 8, duration: 15 },
            formulaFeeding: { frequency: 6, amount: 60 }
          }
        }

        // 检查收藏状态
        const favoriteKey = `favorite_${this.data.monthAge}`
        const isFavorited = wx.getStorageSync(favoriteKey) || false

        // 计算实际章节数量
        const sectionCount = this.calculateSectionCount(defaultDiary)

        this.setData({
          diary: defaultDiary,
          loading: false,
          isFavorited: isFavorited,
          sectionCount: sectionCount
        })
        return
      }

      // 检查收藏状态
      const favoriteKey = `favorite_${this.data.monthAge}`
      const isFavorited = wx.getStorageSync(favoriteKey) || false

      // 计算实际章节数量
      const sectionCount = this.calculateSectionCount(diary)

      this.setData({
        diary,
        loading: false,
        isFavorited: isFavorited,
        sectionCount: sectionCount
      })
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: diary.title
      })
    } catch (error) {
      console.error('加载百科日记失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 计算章节数量
  calculateSectionCount(diary) {
    if (!diary) return 0

    let count = 0

    // 检查各个章节是否有内容
    if (diary.careRequirements && Object.keys(diary.careRequirements).length > 0) count++
    if (diary.precautions && (diary.precautions.safety || diary.precautions.health)) count++
    if (diary.feeding && Object.keys(diary.feeding).length > 0) count++
    if (diary.milestones && Object.keys(diary.milestones).length > 0) count++
    if (diary.faqs && diary.faqs.length > 0) count++

    return count
  },

  // 切换章节展开状态
  toggleSection(e) {
    const section = e.currentTarget.dataset.section
    const key = 'expandedSections.' + section

    const updateData = {}
    updateData[key] = !this.data.expandedSections[section]

    this.setData(updateData)
  },



  // 复制文本
  copyText(e) {
    const text = e.currentTarget.dataset.text
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  // 收藏功能
  toggleFavorite() {
    const currentState = this.data.isFavorited
    const newState = !currentState

    this.setData({
      isFavorited: newState
    })

    // 保存收藏状态到本地存储
    const favoriteKey = `favorite_${this.data.monthAge}`
    wx.setStorageSync(favoriteKey, newState)

    // 显示提示
    wx.showToast({
      title: newState ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `育儿百科 - ${this.data.diary ? this.data.diary.title : '育儿指导'}`,
      path: `/pages/encyclopedia-detail/encyclopedia-detail?monthAge=${this.data.monthAge}`
    }
  },

  onShareTimeline() {
    return {
      title: `育儿百科 - ${this.data.diary ? this.data.diary.title : '育儿指导'}`
    }
  },

  // 查看相关月龄
  viewRelatedAge(e) {
    const monthAge = e.currentTarget.dataset.monthAge

    wx.redirectTo({
      url: `/pages/encyclopedia-detail/encyclopedia-detail?monthAge=${monthAge}`
    })
  },



  // 反馈问题
  feedbackIssue() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },



  // 分享页面
  sharePage() {
    // 直接触发分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  }
})
