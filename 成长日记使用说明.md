# 成长日记功能使用说明

## 功能概述

成长日记是育儿补贴计算器小程序的新增功能，帮助家长详细记录宝宝每个月的成长情况，包括呵护要求、注意事项、饮食、排尿、排便、衣服等各个方面。

## 主要特性

### 📖 月度记录
- 以月为单位记录宝宝的成长情况
- 支持多个孩子的独立记录
- 自动按时间排序显示历史记录

### 💝 全面记录内容
- **呵护要求**：皮肤护理、口腔护理、洗澡护理、睡眠护理、情感呵护等
- **注意事项**：安全、健康、发育、环境、互动等方面的注意事项
- **饮食记录**：母乳喂养、奶粉喂养、辅食添加、饮水量、营养补充等
- **排尿记录**：频次、颜色、气味、换尿布次数等
- **排便记录**：频次、性状、颜色、是否困难等
- **衣服记录**：尺码信息、材质偏好、特殊需求等
- **发育里程碑**：身体、认知、语言、社交、情感、运动发育
- **健康记录**：体重、身高、头围、体温、疫苗、疾病、用药等
- **睡眠记录**：夜间睡眠、白天小睡、睡眠环境等
- **活动记录**：室内外活动、教育活动、社交活动等
- **家长心得**：观察记录、成就记录、挑战、育儿心得等

### 📱 便捷操作
- 直观的月份选择器
- 分章节的表单设计
- 可展开/收起的详情显示
- 一键保存和编辑功能

## 页面结构

### 1. 成长日记主页面 (`pages/growth-diary/growth-diary`)
- 显示统计信息（总记录数、本月状态、最近活动）
- 月份选择器（年份和月份）
- 当前月份日记状态
- 历史记录列表
- 支持新建、查看、编辑、删除操作

### 2. 日记详情页面 (`pages/diary-detail/diary-detail`)
- 分章节展示日记内容
- 可展开/收起各个章节
- 支持编辑和删除操作
- 分享功能

### 3. 日记编辑页面 (`pages/diary-edit/diary-edit`)
- 分章节的表单设计
- 支持文本输入、数字输入、选择器、开关等多种输入方式
- 实时保存功能
- 数据验证

## 数据模型

### 核心数据结构
```javascript
{
  id: '唯一标识符',
  childId: '关联的孩子ID',
  year: 2025,
  month: 1,
  createTime: '创建时间',
  updateTime: '更新时间',
  
  careRequirements: { /* 呵护要求 */ },
  precautions: { /* 注意事项 */ },
  feeding: { /* 饮食记录 */ },
  urination: { /* 排尿记录 */ },
  defecation: { /* 排便记录 */ },
  clothing: { /* 衣服记录 */ },
  milestones: { /* 发育里程碑 */ },
  health: { /* 健康记录 */ },
  sleep: { /* 睡眠记录 */ },
  activities: { /* 活动记录 */ },
  photos: { /* 照片记录 */ },
  parentNotes: { /* 家长心得 */ }
}
```

### 数据存储
- 使用微信小程序本地存储 (`wx.getStorageSync`/`wx.setStorageSync`)
- 按孩子ID分别存储：`growth_diary_${childId}`
- 自动按年月排序
- 支持数据导入导出

## 核心功能函数

### `utils/growthDiary.js`
- `createGrowthDiary(childId, year, month)` - 创建新日记
- `saveGrowthDiary(diary)` - 保存日记
- `getGrowthDiary(childId, year, month)` - 获取特定月份日记
- `getGrowthDiaries(childId)` - 获取日记列表
- `deleteGrowthDiary(childId, year, month)` - 删除日记
- `validateGrowthDiary(diary)` - 验证日记数据
- `formatMonth(year, month)` - 格式化月份显示

## 使用流程

### 1. 新建日记
1. 进入成长日记主页面
2. 选择年份和月份
3. 点击"新建"按钮
4. 填写各项记录内容
5. 点击"保存日记"

### 2. 查看日记
1. 在主页面选择要查看的月份
2. 点击日记项目进入详情页面
3. 点击各章节标题展开/收起内容

### 3. 编辑日记
1. 在详情页面点击"编辑"按钮
2. 或在主页面点击"编辑"按钮
3. 修改相关内容
4. 点击"保存日记"

### 4. 删除日记
1. 在详情页面点击"删除"按钮
2. 或在主页面点击"删除"按钮
3. 确认删除操作

## 集成说明

### 导航集成
成长日记已添加到小程序的底部导航栏（tabBar），用户可以直接点击"成长日记"标签进入。

### 页面路由
- 主页面：`/pages/growth-diary/growth-diary`
- 详情页面：`/pages/diary-detail/diary-detail?childId=xxx&year=xxx&month=xxx`
- 编辑页面：`/pages/diary-edit/diary-edit?childId=xxx&year=xxx&month=xxx&mode=create/edit`

### 样式设计
- 遵循小程序整体设计风格
- 使用统一的颜色变量和组件样式
- 响应式设计，适配不同屏幕尺寸

## 测试验证

项目包含完整的测试文件 `test-growth-diary.js`，验证了以下功能：
- ✅ 创建新日记
- ✅ 数据模型验证
- ✅ 保存和获取日记
- ✅ 日记列表管理
- ✅ 删除功能
- ✅ 格式化函数

## 扩展建议

### 未来可以添加的功能
1. **照片上传**：支持上传宝宝照片到日记中
2. **数据导出**：导出为PDF或Excel格式
3. **云端同步**：支持多设备数据同步
4. **模板功能**：提供常用记录模板
5. **提醒功能**：定期提醒记录日记
6. **数据分析**：生成成长趋势图表
7. **分享功能**：分享给家人或医生

### 性能优化
1. 大量数据时的分页加载
2. 图片压缩和缓存
3. 数据备份和恢复机制

## 注意事项

1. **数据隐私**：所有数据存储在本地，确保隐私安全
2. **存储限制**：注意微信小程序本地存储的大小限制
3. **数据备份**：建议定期备份重要数据
4. **版本兼容**：确保在不同版本的微信中正常运行

---

成长日记功能为家长提供了一个完整的宝宝成长记录工具，帮助更好地关注和记录宝宝的每一个成长瞬间。
