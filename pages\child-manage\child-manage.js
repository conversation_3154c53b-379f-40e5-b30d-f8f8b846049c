// child-manage.js - 孩子管理页面
const childrenManager = require('../../utils/childrenManager')

Page({
  data: {
    mode: 'add', // add, edit, manage
    childId: '',
    children: [],
    maxDate: '', // 最大日期（今天）

    // 表单数据
    formData: {
      name: '',
      birthDate: '',
      gender: '',
      nickname: ''
    },

    // 性别选项
    genderOptions: ['男', '女', '暂不设置'],

    // 验证状态
    errors: {},
    saving: false
  },

  onLoad(options) {
    const mode = options.mode || 'add'
    const childId = options.childId || ''

    // 设置最大日期为今天
    const today = new Date()
    const maxDate = today.toISOString().split('T')[0]

    this.setData({
      mode,
      childId,
      maxDate
    })

    if (mode === 'manage') {
      this.loadChildren()
    } else if (mode === 'edit' && childId) {
      this.loadChildData(childId)
    }

    // 设置导航栏标题
    const titles = {
      add: '添加宝宝',
      edit: '编辑宝宝信息',
      manage: '管理宝宝'
    }
    wx.setNavigationBarTitle({
      title: titles[mode] || '宝宝管理'
    })
  },

  // 加载孩子列表
  loadChildren() {
    const children = childrenManager.getAllChildren()
    this.setData({ children })
  },

  // 加载孩子数据
  loadChildData(childId) {
    const child = childrenManager.getChildById(childId)
    if (child) {
      this.setData({
        formData: {
          name: child.name,
          birthDate: child.birthDate,
          gender: child.gender,
          nickname: child.nickname
        }
      })
    } else {
      wx.showToast({
        title: '孩子信息不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value

    const updateData = {}
    updateData['formData.' + field] = value
    updateData['errors.' + field] = '' // 清除错误信息

    this.setData(updateData)
  },

  // 出生日期变化
  onBirthDateChange(e) {
    this.setData({
      'formData.birthDate': e.detail.value,
      'errors.birthDate': ''
    })
  },

  // 性别选择
  onGenderChange(e) {
    const index = e.detail.value
    const gender = this.data.genderOptions[index]
    
    this.setData({
      'formData.gender': gender,
      'errors.gender': ''
    })
  },

  // 验证表单
  validateForm() {
    const formData = this.data.formData
    const validation = childrenManager.validateChild(formData)
    
    if (!validation.valid) {
      const errors = {}
      validation.errors.forEach(error => {
        if (error.includes('姓名')) {
          errors.name = error
        } else if (error.includes('出生日期')) {
          errors.birthDate = error
        }
      })
      
      this.setData({ errors })
      wx.showToast({
        title: validation.errors[0],
        icon: 'error'
      })
      return false
    }
    
    return true
  },

  // 保存孩子信息
  saveChild() {
    if (this.data.saving) return
    
    if (!this.validateForm()) return
    
    this.setData({ saving: true })
    
    try {
      let success = false
      
      if (this.data.mode === 'add') {
        const newChild = childrenManager.addChild(this.data.formData)
        success = !!newChild
      } else if (this.data.mode === 'edit') {
        success = childrenManager.updateChild(this.data.childId, this.data.formData)
      }
      
      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('保存孩子信息失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
    
    this.setData({ saving: false })
  },

  // 删除孩子
  deleteChild(e) {
    const childId = e.currentTarget.dataset.childId
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个宝宝的信息吗？相关的成长日记也会被删除。',
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          const success = childrenManager.deleteChild(childId)
          
          if (success) {
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            this.loadChildren()
          } else {
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 编辑孩子
  editChild(e) {
    const childId = e.currentTarget.dataset.childId

    // 如果当前已经是编辑模式，直接加载数据而不跳转
    if (this.data.mode === 'edit') {
      this.setData({ childId })
      this.loadChildData(childId)
    } else {
      wx.navigateTo({
        url: `/pages/child-manage/child-manage?mode=edit&childId=${childId}`
      })
    }
  },

  // 取消操作
  cancel() {
    wx.navigateBack()
  },

  // 获取年龄显示
  getChildAgeText(birthDate) {
    return childrenManager.formatAge(birthDate)
  },

  // 跳转到添加孩子页面
  navigateToAddChild() {
    wx.navigateTo({
      url: '/pages/child-manage/child-manage?mode=add'
    })
  }
})
