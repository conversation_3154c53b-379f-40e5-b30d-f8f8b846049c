<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>育儿百科 - 现代化UI设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #f8fafc;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 头部设计 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 60px 24px 40px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            font-size: 18px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(-2px);
        }

        .title-section {
            color: white;
        }

        .main-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .stats-row {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .stat-badge {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 25px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
        }

        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* 内容区域 */
        .content {
            padding: 24px;
            margin-top: -20px;
            position: relative;
            z-index: 3;
        }

        .section-card {
            background: white;
            border-radius: 20px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(255,255,255,0.8);
            transition: all 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.12);
        }

        .section-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .section-header:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        }

        .section-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .section-name {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .toggle-icon {
            font-size: 14px;
            color: #64748b;
            transition: transform 0.3s ease;
        }

        .section-content {
            padding: 24px;
            background: white;
        }

        .info-grid {
            display: grid;
            gap: 16px;
        }

        .info-item {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 16px 20px;
            border-radius: 12px;
            border-left: 4px solid #4f46e5;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
        }

        .info-label {
            font-size: 14px;
            color: #4f46e5;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 16px;
            color: #1e293b;
            line-height: 1.5;
        }

        /* 特殊样式 */
        .highlight-card {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
        }

        .highlight-card .section-header {
            background: rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .highlight-card .section-name {
            color: white;
        }

        .highlight-card .info-item {
            background: rgba(255,255,255,0.1);
            border-left: 4px solid rgba(255,255,255,0.5);
        }

        .highlight-card .info-label {
            color: rgba(255,255,255,0.9);
        }

        .highlight-card .info-value {
            color: white;
        }

        /* 里程碑样式 */
        .milestone-grid {
            display: grid;
            gap: 16px;
        }

        .milestone-item {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 16px;
            border-radius: 12px;
            border-left: 4px solid #0ea5e9;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .milestone-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        }

        .milestone-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .milestone-text {
            font-size: 15px;
            color: #0c4a6e;
            line-height: 1.5;
        }

        /* FAQ样式 */
        .faq-item {
            background: #fefefe;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }

        .faq-question {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .faq-q-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .faq-question-text {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            line-height: 1.4;
        }

        .faq-answer {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding-left: 40px;
        }

        .faq-answer-text {
            font-size: 15px;
            color: #475569;
            line-height: 1.6;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            gap: 8px;
            padding: 16px 24px 0;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 50px 20px 30px;
            }
            
            .main-title {
                font-size: 24px;
            }
            
            .content {
                padding: 20px 16px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="header-content">
                <button class="back-btn">←</button>
                
                <div class="title-section">
                    <h1 class="main-title">📚 新生儿期（0-1个月）</h1>
                    <p class="subtitle">专业的育儿指导和建议</p>

                    <div class="stats-row">
                        <div class="stat-badge">👶 0-1月龄</div>
                        <div class="stat-badge">📋 8个章节</div>
                        <div class="stat-badge">⭐ 专业指导</div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary">
                            <span>⭐</span>
                            <span>收藏</span>
                        </button>
                        <button class="btn btn-secondary">
                            <span>📤</span>
                            <span>分享</span>
                        </button>
                    </div>
                </div>

                <!-- 导航标签 -->
                <div class="nav-tabs">
                    <div class="nav-tab active">💝 呵护要求</div>
                    <div class="nav-tab">⚠️ 注意事项</div>
                    <div class="nav-tab">🍼 饮食指导</div>
                    <div class="nav-tab">📈 发育里程碑</div>
                    <div class="nav-tab">❓ 常见问题</div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 呵护要求 - 高亮卡片 -->
            <div class="section-card highlight-card">
                <div class="section-header">
                    <div class="section-title">
                        <div class="section-title-left">
                            <div class="section-icon">💝</div>
                            <span class="section-name">呵护要求</span>
                        </div>
                        <span class="toggle-icon">▼</span>
                    </div>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">皮肤护理</div>
                            <div class="info-value">每天用温水轻柔清洁，避免使用刺激性产品。保持皮肤干燥，及时更换尿布。</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">睡眠护理</div>
                            <div class="info-value">每天睡眠16-20小时，以仰卧位为主。保持安静的睡眠环境，避免强光刺激。</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发育里程碑 -->
            <div class="section-card">
                <div class="section-header">
                    <div class="section-title">
                        <div class="section-title-left">
                            <div class="section-icon">📈</div>
                            <span class="section-name">发育里程碑</span>
                        </div>
                        <span class="toggle-icon">▼</span>
                    </div>
                </div>
                <div class="section-content">
                    <div class="milestone-grid">
                        <div class="milestone-item">
                            <div class="milestone-icon">💪</div>
                            <div class="milestone-text">体重增长600-800g/月，身长增长2.5-3cm/月</div>
                        </div>
                        <div class="milestone-item">
                            <div class="milestone-icon">👁️</div>
                            <div class="milestone-text">对光线有反应，能短暂注视物体</div>
                        </div>
                        <div class="milestone-item">
                            <div class="milestone-icon">👂</div>
                            <div class="milestone-text">发出简单声音，对声音有反应</div>
                        </div>
                        <div class="milestone-item">
                            <div class="milestone-icon">😊</div>
                            <div class="milestone-text">开始有社会性微笑，能与人眼神交流</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 常见问题 -->
            <div class="section-card">
                <div class="section-header">
                    <div class="section-title">
                        <div class="section-title-left">
                            <div class="section-icon">❓</div>
                            <span class="section-name">常见问题</span>
                        </div>
                        <span class="toggle-icon">▼</span>
                    </div>
                </div>
                <div class="section-content">
                    <div class="faq-item">
                        <div class="faq-question">
                            <div class="faq-q-icon">Q</div>
                            <div class="faq-question-text">新生儿一天应该睡多少小时？</div>
                        </div>
                        <div class="faq-answer">
                            <div class="faq-answer-text">新生儿每天需要睡眠16-20小时，这是正常的。他们的睡眠周期较短，通常每次睡2-4小时就会醒来。</div>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <div class="faq-q-icon">Q</div>
                            <div class="faq-question-text">宝宝哭闹不止怎么办？</div>
                        </div>
                        <div class="faq-answer">
                            <div class="faq-answer-text">首先检查是否饿了、尿布是否需要更换、是否太热或太冷。如果基本需求都满足了，可以尝试轻柔的摇摆、唱歌或播放白噪音。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.querySelectorAll('.section-header').forEach(header => {
            header.addEventListener('click', function() {
                const toggleIcon = this.querySelector('.toggle-icon');
                const content = this.nextElementSibling;
                
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    toggleIcon.textContent = '▼';
                } else {
                    content.style.display = 'none';
                    toggleIcon.textContent = '▶';
                }
            });
        });
    </script>
</body>
</html>
