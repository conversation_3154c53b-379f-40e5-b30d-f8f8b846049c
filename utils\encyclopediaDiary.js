// 百科日记数据模型和工具函数
const app = getApp()

/**
 * 百科日记数据模型 - 按月龄组织的标准育儿指导
 */
const EncyclopediaDiaryModel = {
  monthAge: 0, // 月龄 (0-36)
  title: '', // 标题，如"新生儿期（0-1个月）"
  
  // 呵护要求
  careRequirements: {
    skinCare: '', // 皮肤护理指导
    oralCare: '', // 口腔护理指导
    bathingCare: '', // 洗澡护理指导
    sleepCare: '', // 睡眠护理指导
    exerciseCare: '', // 运动护理指导
    emotionalCare: '', // 情感呵护指导
    generalCare: '' // 一般护理要求
  },
  
  // 注意事项
  precautions: {
    safety: [], // 安全注意事项
    health: [], // 健康注意事项
    development: [], // 发育注意事项
    environment: [], // 环境注意事项
    interaction: [], // 互动注意事项
    emergency: [] // 紧急情况处理
  },
  
  // 饮食指导
  feeding: {
    breastfeeding: {
      frequency: '', // 母乳喂养频次指导
      duration: '', // 每次喂养时长指导
      tips: '', // 母乳喂养技巧
      problems: [] // 常见问题及解决方案
    },
    formulaFeeding: {
      frequency: '', // 奶粉喂养频次指导
      amount: '', // 奶量指导
      preparation: '', // 冲调方法
      tips: '' // 奶粉喂养注意事项
    },
    complementaryFood: {
      introduced: false, // 是否可以添加辅食
      foods: [], // 推荐食物
      forbidden: [], // 禁忌食物
      schedule: '', // 喂养时间安排
      preparation: '', // 制作方法
      tips: '' // 辅食添加技巧
    },
    water: {
      needed: false, // 是否需要额外饮水
      amount: '', // 饮水量指导
      tips: '' // 饮水注意事项
    },
    supplements: {
      vitaminD: '', // 维生素D补充指导
      iron: '', // 铁剂补充指导
      calcium: '', // 钙剂补充指导
      others: [], // 其他营养补充
      tips: '' // 营养补充注意事项
    }
  },
  
  // 排尿指导
  urination: {
    frequency: '', // 正常排尿频次
    color: '', // 正常尿液颜色
    amount: '', // 正常尿量
    diaperChanges: '', // 换尿布频次
    abnormalSigns: [], // 异常征象
    tips: '' // 护理要点
  },
  
  // 排便指导
  defecation: {
    frequency: '', // 正常排便频次
    consistency: '', // 正常便便性状
    color: '', // 正常颜色
    abnormalSigns: [], // 异常征象
    constipationPrevention: '', // 便秘预防
    tips: '' // 护理要点
  },
  
  // 衣着建议
  clothing: {
    materials: [], // 推荐材质
    layers: '', // 穿衣层数建议
    sizes: '', // 尺码选择指导
    seasonalTips: {
      spring: '', // 春季穿衣建议
      summer: '', // 夏季穿衣建议
      autumn: '', // 秋季穿衣建议
      winter: '' // 冬季穿衣建议
    },
    specialNeeds: '', // 特殊需求
    washingTips: '' // 清洗保养
  },
  
  // 发育里程碑
  milestones: {
    physical: [], // 身体发育里程碑
    cognitive: [], // 认知发育里程碑
    language: [], // 语言发育里程碑
    social: [], // 社交发育里程碑
    emotional: [], // 情感发育里程碑
    motor: [] // 运动发育里程碑
  },
  
  // 健康指标
  health: {
    weight: '', // 体重范围
    height: '', // 身高范围
    headCircumference: '', // 头围范围
    temperature: '', // 正常体温
    vaccinations: [], // 疫苗接种计划
    commonIllnesses: [], // 常见疾病及处理
    warningSigns: [], // 需要就医的征象
    checkupSchedule: '' // 体检时间安排
  },
  
  // 睡眠指导
  sleep: {
    totalHours: '', // 每日总睡眠时间
    nightSleep: '', // 夜间睡眠时长
    napSchedule: '', // 白天小睡安排
    sleepEnvironment: '', // 睡眠环境要求
    sleepTraining: '', // 睡眠训练方法
    commonProblems: [], // 常见睡眠问题
    tips: '' // 睡眠护理要点
  },
  
  // 活动建议
  activities: {
    indoor: [], // 室内活动推荐
    outdoor: [], // 户外活动推荐
    educational: [], // 教育活动推荐
    sensory: [], // 感官刺激活动
    motor: [], // 运动发展活动
    social: [], // 社交活动
    safety: '', // 活动安全注意事项
    duration: '' // 活动时长建议
  },
  
  // 常见问题FAQ
  faqs: [
    {
      question: '',
      answer: '',
      tips: '',
      category: '' // 分类：喂养、睡眠、健康、发育等
    }
  ],
  
  // 专家建议
  expertTips: {
    pediatrician: '', // 儿科医生建议
    nutritionist: '', // 营养师建议
    psychologist: '', // 心理学家建议
    educator: '' // 早教专家建议
  },
  
  // 相关资源
  resources: {
    articles: [], // 相关文章
    videos: [], // 推荐视频
    books: [], // 推荐书籍
    apps: [], // 推荐应用
    websites: [] // 推荐网站
  }
}

/**
 * 获取百科日记数据
 * @returns {Object} 完整的百科日记数据
 */
function getEncyclopediaData() {
  return {
    0: { // 新生儿期（0-1个月）
      monthAge: 0,
      title: '新生儿期（0-1个月）',
      careRequirements: {
        skinCare: '新生儿皮肤娇嫩，需要温和护理。每天用温水轻柔清洁，避免使用刺激性产品。保持皮肤干燥，及时更换尿布。',
        oralCare: '新生儿无需特殊口腔护理，喂奶后可用温开水清洁口腔。注意观察口腔内是否有异常白斑。',
        bathingCare: '每天或隔天洗澡，水温37-40℃，时间5-10分钟。动作要轻柔，注意保暖。',
        sleepCare: '新生儿每天睡眠16-20小时，以仰卧位为主。保持安静的睡眠环境，避免强光刺激。',
        exerciseCare: '适当的俯卧练习有助于颈部肌肉发育，每天2-3次，每次3-5分钟。',
        emotionalCare: '及时回应宝宝的需求，多与宝宝说话、唱歌，建立亲子关系。',
        generalCare: '保持室温22-26℃，湿度50-60%。定期检查体重增长情况。'
      },
      precautions: {
        safety: ['避免摇晃宝宝', '确保睡眠环境安全', '正确抱姿', '防止跌落'],
        health: ['观察黄疸情况', '注意体温变化', '监测呼吸状况', '观察大小便'],
        development: ['关注反射动作', '观察视觉追踪', '注意听觉反应'],
        environment: ['保持室内空气流通', '避免噪音干扰', '控制探访人数'],
        interaction: ['轻柔的抚触', '温和的语调', '适当的视觉刺激'],
        emergency: ['发热超过38℃立即就医', '呼吸困难', '持续哭闹不止', '拒绝进食']
      },
      feeding: {
        breastfeeding: {
          frequency: '每2-3小时喂养一次，每天8-12次',
          duration: '每次15-30分钟',
          tips: '按需喂养，观察宝宝饥饿信号。确保正确的含乳姿势。',
          problems: ['乳头疼痛', '奶水不足', '宝宝拒绝吸吮']
        },
        formulaFeeding: {
          frequency: '每3-4小时喂养一次',
          amount: '每次60-90ml',
          preparation: '严格按比例冲调，水温40-50℃',
          tips: '奶瓶和奶嘴要彻底消毒，现冲现喝。'
        },
        complementaryFood: {
          introduced: false,
          foods: [],
          forbidden: ['蜂蜜', '牛奶', '坚果', '蛋白'],
          schedule: '暂不添加辅食',
          preparation: '',
          tips: '6个月前纯母乳或配方奶喂养'
        },
        water: {
          needed: false,
          amount: '无需额外饮水',
          tips: '母乳和配方奶已提供足够水分'
        },
        supplements: {
          vitaminD: '每日400IU维生素D',
          iron: '母乳喂养儿可能需要补铁',
          calcium: '通过母乳或配方奶获得',
          others: [],
          tips: '遵医嘱补充营养素'
        }
      },
      urination: {
        frequency: '每天6-8次',
        color: '淡黄色或无色',
        amount: '每次少量',
        diaperChanges: '每天8-10次',
        abnormalSigns: ['尿液深黄', '排尿困难', '尿量明显减少'],
        tips: '及时更换尿布，保持干爽'
      },
      defecation: {
        frequency: '母乳喂养：每天3-8次；配方奶：每天1-4次',
        consistency: '软便或糊状',
        color: '黄色或金黄色',
        abnormalSigns: ['便血', '白色便', '黑色便', '水样便'],
        constipationPrevention: '确保充足的奶量摄入',
        tips: '观察便便性状和频次的变化'
      },
      milestones: {
        physical: ['体重增长600-800g/月', '身长增长2.5-3cm/月'],
        cognitive: ['对光线有反应', '能短暂注视物体'],
        language: ['发出简单声音', '对声音有反应'],
        social: ['开始有社会性微笑', '能与人眼神交流'],
        emotional: ['表达基本需求', '对抚慰有反应'],
        motor: ['原始反射存在', '俯卧时能短暂抬头']
      },
      faqs: [
        {
          question: '新生儿黄疸什么时候消退？',
          answer: '生理性黄疸通常在出生后2-3天出现，7-10天消退。如果黄疸持续超过2周或程度较重，需要就医检查。',
          tips: '多晒太阳有助于黄疸消退，但要避免直射。',
          category: '健康'
        },
        {
          question: '新生儿一天要睡多久？',
          answer: '新生儿每天需要睡眠16-20小时，这是正常的。睡眠时间会随着月龄增长逐渐减少。',
          tips: '建立规律的睡眠环境，有助于宝宝形成良好的睡眠习惯。',
          category: '睡眠'
        }
      ]
    },

    1: { // 1个月
      monthAge: 1,
      title: '1个月',
      careRequirements: {
        skinCare: '继续温和护理，注意颈部、腋下等皱褶处的清洁。可以开始使用婴儿润肤露。',
        oralCare: '喂奶后用纱布蘸温开水轻拭口腔，预防鹅口疮。',
        bathingCare: '可以适当延长洗澡时间到10-15分钟，增加亲子互动。',
        sleepCare: '开始建立昼夜节律，白天保持光线充足，夜间保持安静昏暗。',
        exerciseCare: '增加俯卧时间，每天3-4次，每次5-10分钟。开始简单的被动操。',
        emotionalCare: '多与宝宝对话，模仿宝宝的声音，促进语言发展。',
        generalCare: '定期测量体重身长，关注生长发育情况。'
      },
      milestones: {
        physical: ['能稳定抬头2-3秒', '手脚活动更加协调'],
        cognitive: ['能追视移动物体', '对熟悉声音有反应'],
        language: ['发出更多元音', '开始咿呀学语'],
        social: ['社会性微笑更明显', '能识别主要照护者'],
        emotional: ['情绪表达更丰富', '对安抚有明显反应'],
        motor: ['握拳反射开始减弱', '俯卧抬头更稳定']
      },
      faqs: [
        {
          question: '1个月宝宝应该增重多少？',
          answer: '1个月宝宝通常增重600-1000g，身长增长3-4cm。如果增重不足，需要评估喂养情况。',
          tips: '定期称重，记录生长曲线，有助于及时发现问题。',
          category: '发育'
        }
      ]
    },

    2: { // 2个月
      monthAge: 2,
      title: '2个月',
      careRequirements: {
        skinCare: '继续温和护理，注意颈部、腋下等皱褶处的清洁。可以开始使用婴儿润肤露，预防湿疹。',
        oralCare: '喂奶后用纱布蘸温开水轻拭口腔，观察是否有鹅口疮等异常。',
        bathingCare: '洗澡时间可延长到10-15分钟，水温保持37-40℃，增加亲子互动。',
        sleepCare: '开始建立昼夜节律，白天保持光线充足，夜间保持安静昏暗。夜间睡眠可达4-6小时。',
        exerciseCare: '增加俯卧时间，每天3-4次，每次5-10分钟。开始简单的被动操和抚触。',
        emotionalCare: '多与宝宝对话，模仿宝宝的声音，促进语言发展。回应宝宝的微笑。',
        generalCare: '定期测量体重身长，关注生长发育情况。注意观察宝宝的反应和行为变化。'
      },
      precautions: {
        safety: ['避免摇晃宝宝', '确保睡眠环境安全', '正确抱姿', '防止跌落', '注意室内温度'],
        health: ['观察疫苗接种反应', '注意体温变化', '监测呼吸状况', '观察大小便', '关注皮肤状况'],
        development: ['关注社会性微笑', '观察视觉追踪能力', '注意听觉反应', '观察头部控制能力'],
        environment: ['保持室内空气流通', '避免噪音干扰', '控制探访人数', '注意卫生清洁'],
        interaction: ['适当的视觉刺激', '温和的语调交流', '规律的日常护理', '及时回应需求'],
        emergency: ['发热超过38℃立即就医', '呼吸困难', '持续哭闹不止', '拒绝进食', '皮疹或异常症状']
      },
      feeding: {
        breastfeeding: {
          frequency: '每2-3小时喂养一次，每天6-8次',
          duration: '每次15-25分钟',
          tips: '按需喂养，观察宝宝饥饿信号。建立规律的喂养时间。',
          problems: ['乳头疼痛', '奶水不足感', '宝宝拒绝吸吮', '乳腺炎']
        },
        formulaFeeding: {
          frequency: '每3-4小时喂养一次',
          amount: '每次90-120ml',
          preparation: '严格按比例冲调，水温40-50℃',
          tips: '奶瓶和奶嘴要彻底消毒，现冲现喝。观察宝宝饱腹信号。'
        },
        complementaryFood: {
          introduced: false,
          foods: [],
          forbidden: ['蜂蜜', '牛奶', '坚果', '蛋白', '任何固体食物'],
          schedule: '暂不添加辅食',
          preparation: '',
          tips: '6个月前纯母乳或配方奶喂养，不需要额外饮水'
        },
        water: {
          needed: false,
          amount: '无需额外饮水',
          tips: '母乳和配方奶已提供足够水分，额外饮水可能影响奶量摄入'
        },
        supplements: {
          vitaminD: '每日400IU维生素D',
          iron: '母乳喂养儿从4个月开始可能需要补铁',
          calcium: '通过母乳或配方奶获得',
          others: [],
          tips: '遵医嘱补充营养素，不要自行添加'
        }
      },
      urination: {
        frequency: '每天6-8次',
        color: '淡黄色或无色',
        amount: '每次适量',
        diaperChanges: '每天8-10次',
        abnormalSigns: ['尿液深黄', '排尿困难', '尿量明显减少', '尿布疹'],
        tips: '及时更换尿布，保持干爽，使用护臀膏预防尿布疹'
      },
      defecation: {
        frequency: '母乳喂养：每天2-5次；配方奶：每天1-3次',
        consistency: '软便或糊状',
        color: '黄色或金黄色',
        abnormalSigns: ['便血', '白色便', '黑色便', '水样便', '便秘超过3天'],
        constipationPrevention: '确保充足的奶量摄入，适当腹部按摩',
        tips: '观察便便性状和频次的变化，记录排便规律'
      },
      milestones: {
        physical: ['体重增长600-800g/月', '身长增长2.5-3cm/月', '头部控制能力增强'],
        cognitive: ['能追视移动物体90度', '对声音反应更敏感', '开始有记忆能力'],
        language: ['发出更多元音', '开始咿呀学语', '对熟悉声音有明显反应'],
        social: ['社会性微笑更明显', '能与人眼神交流', '开始识别主要照护者'],
        emotional: ['情绪表达更丰富', '对安抚有明显反应', '开始表现出不同的哭声'],
        motor: ['俯卧抬头更稳定', '握拳反射开始减弱', '手脚活动更协调']
      },
      faqs: [
        {
          question: '2个月宝宝应该增重多少？',
          answer: '2个月宝宝通常增重600-1000g，身长增长3-4cm。如果增重不足，需要评估喂养情况。',
          tips: '定期称重，记录生长曲线，有助于及时发现问题。',
          category: '发育'
        },
        {
          question: '宝宝什么时候开始有社会性微笑？',
          answer: '大多数宝宝在6-8周（1.5-2个月）开始出现社会性微笑，这是重要的社交发育里程碑。',
          tips: '多与宝宝互动，用夸张的表情和声音与宝宝交流。',
          category: '发育'
        },
        {
          question: '2个月宝宝需要接种什么疫苗？',
          answer: '按照国家免疫程序，2个月时需要接种脊髓灰质炎疫苗和百白破疫苗。',
          tips: '接种前确保宝宝身体健康，接种后观察是否有不良反应。',
          category: '健康'
        }
      ]
    },

    3: { // 3个月
      monthAge: 3,
      title: '3个月',
      careRequirements: {
        skinCare: '注意保湿，特别是干燥季节。选择温和的婴幼儿专用护肤品，避免成人用品。',
        oralCare: '继续口腔清洁，可以开始让宝宝适应清洁的感觉，为将来刷牙做准备。',
        bathingCare: '洗澡时间可延长到15-20分钟，可以增加一些水中玩具，让洗澡变得有趣。',
        sleepCare: '夜间睡眠可达5-6小时，开始建立更规律的睡眠模式。白天小睡3-4次。',
        exerciseCare: '增加俯卧时间，鼓励抬头练习。开始简单的四肢运动，促进肌肉发育。',
        emotionalCare: '增加互动游戏，如躲猫猫的雏形。多给宝宝唱歌，建立安全感。',
        generalCare: '开始建立日常作息规律，有助于宝宝适应环境和建立安全感。'
      },
      feeding: {
        breastfeeding: {
          frequency: '每3-4小时喂养一次，每天5-6次',
          duration: '每次15-20分钟',
          tips: '宝宝吃奶效率提高，时间可能缩短。注意观察宝宝的生长情况。',
          problems: ['生长突增期需求增加', '宝宝注意力分散', '夜奶次数可能增加']
        },
        formulaFeeding: {
          frequency: '每3-4小时喂养一次',
          amount: '每次120-150ml',
          preparation: '继续严格按比例冲调，注意奶瓶清洁',
          tips: '可以开始尝试定时喂养，但仍要根据宝宝需求调整。'
        }
      },
      milestones: {
        physical: ['俯卧时能稳定抬头45-90度', '开始有翻身的迹象', '体重约为出生时的2倍'],
        cognitive: ['能追视物体180度', '开始有因果关系概念', '注意力集中时间延长'],
        language: ['发出更多辅音', '开始模仿声音', '对音乐有反应'],
        social: ['喜欢看人脸', '对熟悉的人有明显偏好', '开始有幽默感的雏形'],
        emotional: ['情绪表达更加丰富', '开始有期待感', '对日常活动有预期'],
        motor: ['手部抓握能力增强', '开始将手放到嘴里', '腿部力量增强']
      },
      faqs: [
        {
          question: '3个月宝宝一天应该睡多久？',
          answer: '3个月宝宝每天总睡眠时间约14-17小时，夜间可连续睡5-6小时，白天小睡3-4次。',
          tips: '建立固定的睡前仪式，有助于宝宝形成良好的睡眠习惯。',
          category: '睡眠'
        },
        {
          question: '宝宝什么时候开始翻身？',
          answer: '大多数宝宝在3-6个月之间学会翻身，通常先学会从俯卧翻到仰卧。',
          tips: '给宝宝足够的俯卧练习时间，但睡觉时仍要仰卧。',
          category: '发育'
        }
      ]
    },

    4: { // 4个月
      monthAge: 4,
      title: '4个月',
      careRequirements: {
        skinCare: '继续温和护理，注意季节变化对皮肤的影响。可以开始使用防晒措施。',
        oralCare: '可能开始流口水增多，及时擦拭，保持下巴干燥，预防口水疹。',
        bathingCare: '可以尝试坐浴，但需要良好的支撑。洗澡时可以增加更多互动。',
        sleepCare: '夜间睡眠可达6-8小时，白天小睡2-3次。开始睡眠训练的准备。',
        exerciseCare: '鼓励更多的俯卧时间，开始练习支撑体重。提供安全的探索空间。',
        emotionalCare: '增加社交互动，多带宝宝接触不同的环境和人群（在安全范围内）。',
        generalCare: '准备辅食添加的相关用品，观察宝宝对食物的兴趣。'
      },
      feeding: {
        breastfeeding: {
          frequency: '每3-4小时喂养一次，每天5-6次',
          duration: '每次15-20分钟',
          tips: '为即将到来的辅食添加做准备，但仍以母乳为主。',
          problems: ['宝宝可能开始对周围环境更感兴趣', '喂奶时容易分心']
        },
        formulaFeeding: {
          frequency: '每4小时喂养一次',
          amount: '每次150-180ml',
          preparation: '继续严格卫生标准，准备辅食添加用具',
          tips: '观察宝宝是否对大人吃饭表现出兴趣。'
        },
        complementaryFood: {
          introduced: false,
          foods: [],
          forbidden: ['所有固体食物', '蜂蜜', '牛奶', '坚果'],
          schedule: '可以开始准备，但通常6个月开始添加',
          preparation: '准备辅食用具：婴儿勺、碗、围兜等',
          tips: '观察宝宝是否出现辅食添加的准备信号：能稳定坐立、对食物感兴趣等'
        }
      },
      milestones: {
        physical: ['能稳定抬头并保持', '开始尝试翻身', '能短时间支撑上身'],
        cognitive: ['能区分熟人和陌生人', '对镜子中的自己感兴趣', '开始有物体永恒概念'],
        language: ['发出更多音节组合', '开始咿呀学语', '对自己的名字有反应'],
        social: ['喜欢与人互动', '开始模仿面部表情', '对其他宝宝感兴趣'],
        emotional: ['表现出明显的喜好', '对分离开始有反应', '情绪调节能力提高'],
        motor: ['手眼协调改善', '能抓握并摇晃玩具', '腿部力量增强']
      },
      faqs: [
        {
          question: '4个月宝宝可以开始添加辅食吗？',
          answer: '世界卫生组织建议6个月开始添加辅食。4个月时可以观察宝宝是否出现准备信号。',
          tips: '准备信号包括：能稳定坐立、对食物感兴趣、挺舌反射消失等。',
          category: '喂养'
        },
        {
          question: '宝宝开始认生是正常的吗？',
          answer: '4-6个月开始认生是正常的发育表现，说明宝宝能区分熟人和陌生人。',
          tips: '给宝宝时间适应新面孔，不要强迫宝宝与陌生人互动。',
          category: '发育'
        }
      ]
    },

    5: { // 5个月
      monthAge: 5,
      title: '5个月',
      careRequirements: {
        skinCare: '注意保护皮肤，特别是开始活动增多后的摩擦部位。选择透气性好的衣物。',
        oralCare: '口水增多是正常现象，及时更换围兜，保持下巴清洁干燥。',
        bathingCare: '可以增加洗澡的趣味性，使用安全的洗澡玩具，培养对水的喜爱。',
        sleepCare: '夜间睡眠逐渐稳定，可以开始温和的睡眠训练。白天小睡2-3次。',
        exerciseCare: '鼓励翻身练习，提供安全的地面活动空间。开始准备爬行环境。',
        emotionalCare: '增加亲子游戏时间，如简单的躲猫猫游戏，促进情感发展。',
        generalCare: '为即将到来的辅食添加做最后准备，确保用具齐全且清洁。'
      },
      feeding: {
        breastfeeding: {
          frequency: '每4小时喂养一次，每天4-5次',
          duration: '每次15-20分钟',
          tips: '准备在下个月开始添加辅食，但母乳仍是主要营养来源。',
          problems: ['宝宝可能对周围环境更感兴趣', '夜奶可能减少']
        },
        formulaFeeding: {
          frequency: '每4小时喂养一次',
          amount: '每次180-210ml',
          preparation: '继续严格卫生，准备辅食添加的过渡',
          tips: '观察宝宝的饱腹信号，不要强迫喂养。'
        },
        complementaryFood: {
          introduced: false,
          foods: [],
          forbidden: ['所有固体食物', '蜂蜜', '牛奶', '坚果', '蛋白'],
          schedule: '下个月即将开始',
          preparation: '准备婴儿米粉、蔬菜泥制作工具',
          tips: '可以让宝宝观察大人吃饭，培养对食物的兴趣'
        }
      },
      milestones: {
        physical: ['能翻身（俯卧到仰卧）', '坐立时需要支撑', '能抓握并传递物品'],
        cognitive: ['对因果关系有更好理解', '能寻找掉落的物品', '记忆力明显提高'],
        language: ['发出更复杂的音节', '开始模仿语调', '对音乐节拍有反应'],
        social: ['喜欢照镜子', '对其他宝宝表现出兴趣', '开始有分享的概念'],
        emotional: ['表现出明显的个性特征', '对熟悉的活动有期待', '分离焦虑开始出现'],
        motor: ['精细动作发展', '能用整个手掌抓握', '腿部力量足以支撑部分体重']
      },
      faqs: [
        {
          question: '5个月宝宝还不会翻身正常吗？',
          answer: '宝宝发育有个体差异，3-7个月学会翻身都是正常的。可以多做俯卧练习。',
          tips: '给宝宝足够的地面时间，用玩具鼓励翻身动作。',
          category: '发育'
        },
        {
          question: '宝宝流口水很多怎么办？',
          answer: '5-6个月流口水增多是正常现象，与唾液腺发育和即将出牙有关。',
          tips: '及时擦拭，使用柔软的围兜，保持下巴干燥，预防口水疹。',
          category: '护理'
        }
      ]
    },

    6: { // 6个月 - 辅食添加期
      monthAge: 6,
      title: '6个月',
      careRequirements: {
        skinCare: '继续温和护理，注意辅食添加后可能出现的过敏反应。保持皮肤清洁干燥。',
        oralCare: '开始清洁牙龈，为出牙做准备。可以用纱布或硅胶指套轻拭牙龈。',
        bathingCare: '可以坐浴，但需要有支撑。洗澡时间可延长到15-20分钟。',
        sleepCare: '建立更规律的睡眠时间，夜间睡眠可达6-8小时连续。',
        exerciseCare: '鼓励翻身、坐立练习。提供安全的爬行空间。',
        emotionalCare: '增加互动游戏，如躲猫猫。回应宝宝的情感需求。',
        generalCare: '开始添加辅食，观察过敏反应。定期体检。'
      },
      feeding: {
        breastfeeding: {
          frequency: '每天4-6次，配合辅食',
          duration: '每次10-20分钟',
          tips: '辅食前先喂奶，保证营养充足。',
          problems: ['拒绝辅食', '过敏反应', '消化不良']
        },
        complementaryFood: {
          introduced: true,
          foods: ['米粉', '蔬菜泥', '水果泥', '蛋黄'],
          forbidden: ['蜂蜜', '坚果', '海鲜', '蛋白'],
          schedule: '每天1-2次辅食，从少量开始',
          preparation: '食物要煮烂、压碎，质地细腻',
          tips: '一次只添加一种新食物，观察3-5天无过敏反应再添加下一种'
        },
        water: {
          needed: true,
          amount: '每天50-100ml',
          tips: '辅食后可以喝少量水，帮助消化'
        }
      },
      milestones: {
        physical: ['能独立坐立片刻', '开始长牙', '体重约为出生时2倍'],
        cognitive: ['对镜子中的自己感兴趣', '能区分熟人和陌生人'],
        language: ['发出更多音节', '开始模仿声音'],
        social: ['喜欢与人互动', '对自己的名字有反应'],
        emotional: ['表现出明显的喜怒哀乐', '对分离有焦虑'],
        motor: ['能翻身', '开始爬行准备动作', '手眼协调改善']
      },
      faqs: [
        {
          question: '6个月宝宝辅食添加顺序是什么？',
          answer: '建议顺序：米粉→蔬菜泥→水果泥→蛋黄→肉泥。每种食物添加3-5天观察无过敏反应再添加下一种。',
          tips: '从单一食材开始，逐渐增加种类和复杂度。',
          category: '喂养'
        },
        {
          question: '宝宝不爱吃辅食怎么办？',
          answer: '这很正常。可以尝试：改变食物质地、让宝宝参与进食、营造愉快氛围、多次尝试同一食物。',
          tips: '不要强迫进食，保持耐心。有些宝宝需要尝试10-15次才接受新食物。',
          category: '喂养'
        }
      ]
    },

    7: { // 7个月
      monthAge: 7,
      title: '7个月',
      careRequirements: {
        skinCare: '注意辅食添加后可能出现的过敏反应，保持皮肤清洁。活动增多后要注意防护。',
        oralCare: '可能开始出牙，提供安全的磨牙用品。继续口腔清洁，预防蛀牙。',
        bathingCare: '可以坐着洗澡，但仍需密切看护。增加水中玩具，培养洗澡乐趣。',
        sleepCare: '夜间睡眠更稳定，可能出现睡眠倒退。坚持睡前仪式。',
        exerciseCare: '鼓励爬行准备动作，提供安全的探索环境。加强四肢协调训练。',
        emotionalCare: '分离焦虑可能加重，给予更多安全感。建立稳定的日常作息。',
        generalCare: '辅食添加进入新阶段，注意营养均衡和食物安全。'
      },
      feeding: {
        breastfeeding: {
          frequency: '每天4-5次',
          duration: '每次10-15分钟',
          tips: '辅食添加后，母乳仍是重要营养来源，但比例开始下降。',
          problems: ['宝宝可能对辅食更感兴趣', '夜奶可能进一步减少']
        },
        complementaryFood: {
          introduced: true,
          foods: ['米粉', '蔬菜泥', '水果泥', '蛋黄', '肉泥'],
          forbidden: ['蜂蜜', '牛奶', '坚果', '蛋白', '海鲜'],
          schedule: '每天2-3次辅食，配合奶类喂养',
          preparation: '食物应煮熟压碎，质地从泥状逐渐过渡到颗粒状',
          tips: '一次只添加一种新食物，观察3-5天无过敏反应再添加下一种'
        },
        water: {
          needed: true,
          amount: '每天50-100ml',
          tips: '辅食添加后可以给少量白开水，用杯子而不是奶瓶'
        }
      },
      milestones: {
        physical: ['能独立坐立片刻', '开始爬行准备动作', '能用手指捏取小物品'],
        cognitive: ['物体永恒概念发展', '能理解简单指令', '模仿能力增强'],
        language: ['发出双音节', '开始理解"不"的含义', '对音乐有明显反应'],
        social: ['认生现象明显', '喜欢躲猫猫游戏', '开始模仿大人动作'],
        emotional: ['分离焦虑加重', '对熟悉环境有明显偏好', '情绪表达更丰富'],
        motor: ['精细动作发展', '能传递物品', '开始用拇指和食指抓握']
      },
      faqs: [
        {
          question: '7个月宝宝辅食应该怎么安排？',
          answer: '每天2-3次辅食，从泥状食物开始，逐渐增加种类。先添加米粉，再加蔬菜、水果、蛋黄。',
          tips: '每次只添加一种新食物，观察过敏反应。保持食物原味，不加盐糖。',
          category: '喂养'
        },
        {
          question: '宝宝认生很严重怎么办？',
          answer: '7-8个月认生是正常发育表现，说明宝宝能区分熟人和陌生人，有安全意识。',
          tips: '不要强迫宝宝与陌生人互动，给宝宝时间适应，保持耐心。',
          category: '发育'
        }
      ]
    },

    8: { // 8个月
      monthAge: 8,
      title: '8个月',
      careRequirements: {
        skinCare: '活动增多，注意膝盖、手掌等部位的保护。选择合适的爬行服装。',
        oralCare: '出牙期口水增多，及时清洁。可以开始使用指套牙刷清洁牙齿。',
        bathingCare: '可以在浴缸中坐立洗澡，增加水中玩具。注意防滑安全。',
        sleepCare: '建立更规律的睡眠时间，夜间睡眠10-12小时，白天小睡2次。',
        exerciseCare: '鼓励爬行，为站立做准备。提供安全的攀爬环境。',
        emotionalCare: '增加亲子互动游戏，如拍手歌、躲猫猫等。建立安全感。',
        generalCare: '家居安全检查，为宝宝爬行和探索做好安全准备。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['各种蔬菜泥', '水果泥', '肉泥', '鱼泥', '蛋黄', '婴儿面条'],
          forbidden: ['蜂蜜', '牛奶', '坚果', '蛋白', '带壳海鲜'],
          schedule: '每天3次辅食，2-3次奶类',
          preparation: '食物质地可以稍微粗糙，有小颗粒，锻炼咀嚼能力',
          tips: '鼓励宝宝自己抓食，培养自主进食能力'
        }
      },
      milestones: {
        physical: ['能独立坐立', '开始爬行', '能扶物站立'],
        cognitive: ['能寻找隐藏的物品', '理解简单的因果关系', '记忆力明显提高'],
        language: ['能发出"mama""dada"等音', '理解简单词汇', '开始模仿语调'],
        social: ['喜欢与人互动游戏', '开始有分享概念', '对其他宝宝感兴趣'],
        emotional: ['表现出明显的喜好', '对熟悉的人有依恋', '开始有幽默感'],
        motor: ['钳形抓握发展', '能拍手', '开始有目的性动作']
      },
      faqs: [
        {
          question: '8个月宝宝应该会爬了吗？',
          answer: '大多数宝宝在6-10个月学会爬行，8个月会爬是正常的，但也有些宝宝更晚学会。',
          tips: '给宝宝足够的地面时间练习，用玩具鼓励爬行动作。',
          category: '发育'
        }
      ]
    },

    9: { // 9个月
      monthAge: 9,
      title: '9个月',
      careRequirements: {
        skinCare: '爬行增多，注意手脚皮肤保护。选择柔软透气的衣物。',
        oralCare: '可能已出牙，开始建立刷牙习惯。使用婴儿牙刷和无氟牙膏。',
        bathingCare: '洗澡时间可以延长，增加更多互动和游戏。注意水温和安全。',
        sleepCare: '睡眠模式更稳定，可能出现短暂的睡眠倒退。坚持睡前仪式。',
        exerciseCare: '鼓励更多爬行和站立练习。提供安全的攀爬玩具。',
        emotionalCare: '分离焦虑可能达到高峰，给予更多安全感和陪伴。',
        generalCare: '为学步做准备，检查家居安全，移除危险物品。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['手指食物', '软煮蔬菜块', '水果块', '婴儿饼干', '软面条'],
          forbidden: ['蜂蜜', '牛奶', '坚果', '硬糖', '爆米花'],
          schedule: '每天3次正餐，1-2次加餐',
          preparation: '提供更多手指食物，鼓励自主进食',
          tips: '让宝宝参与进食过程，培养良好的饮食习惯'
        }
      },
      milestones: {
        physical: ['爬行熟练', '能扶物站立并移步', '精细动作发展'],
        cognitive: ['物体永恒概念成熟', '能解决简单问题', '模仿能力强'],
        language: ['理解更多词汇', '开始有意义地叫"妈妈""爸爸"', '对指令有反应'],
        social: ['喜欢模仿大人', '开始有合作意识', '对陌生人仍有戒备'],
        emotional: ['情绪表达更复杂', '开始有羞怯感', '对表扬有明显反应'],
        motor: ['能用拇指和食指精确抓握', '开始有释放动作', '手眼协调提高']
      }
    },

    10: { // 10个月
      monthAge: 10,
      title: '10个月',
      careRequirements: {
        skinCare: '活动量大增，注意皮肤清洁和保护。及时更换汗湿的衣物。',
        oralCare: '继续建立刷牙习惯，可以让宝宝模仿刷牙动作。',
        bathingCare: '可以尝试站立洗澡，但需要防滑措施和密切看护。',
        sleepCare: '夜间睡眠稳定，白天可能减少到1-2次小睡。',
        exerciseCare: '鼓励站立和迈步练习，但不要过早使用学步车。',
        emotionalCare: '增加社交机会，但要注意宝宝的接受能力。',
        generalCare: '为即将到来的学步期做准备，确保环境安全。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['家庭食物改良版', '更多手指食物', '软质肉类', '豆腐'],
          forbidden: ['蜂蜜', '牛奶', '坚果', '硬质食物', '调味料'],
          schedule: '接近成人用餐时间，每天3餐2点',
          preparation: '食物质地接近成人食物，但仍需软烂',
          tips: '鼓励使用勺子，培养独立进食技能'
        }
      },
      milestones: {
        physical: ['能独立站立片刻', '扶物行走', '能弯腰捡东西'],
        cognitive: ['理解简单指令', '能完成简单任务', '记忆力进一步发展'],
        language: ['词汇理解快速增长', '开始说第一个有意义的词', '手势交流增多'],
        social: ['喜欢与大人互动', '开始有竞争意识', '对规则有初步理解'],
        emotional: ['自我意识萌芽', '开始有自主性', '对挫折有反应'],
        motor: ['精细动作更精确', '能放开物品', '开始有书写前技能']
      }
    },

    11: { // 11个月
      monthAge: 11,
      title: '11个月',
      careRequirements: {
        skinCare: '即将学步，注意膝盖和手部保护。选择合适的学步鞋。',
        oralCare: '可能已有多颗牙齿，加强口腔清洁。开始使用含氟牙膏。',
        bathingCare: '洗澡时可以增加更多教育性游戏，如认识身体部位。',
        sleepCare: '睡眠时间稳定，为1岁后的作息调整做准备。',
        exerciseCare: '鼓励独立行走尝试，提供安全的练习环境。',
        emotionalCare: '增加语言交流，多读书讲故事，促进语言发展。',
        generalCare: '为1岁生日和新的发展阶段做准备。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['接近成人食物', '更多种类的蛋白质', '全谷物', '奶制品（除牛奶）'],
          forbidden: ['蜂蜜', '牛奶', '坚果', '硬糖', '高盐高糖食物'],
          schedule: '与家庭用餐时间同步',
          preparation: '食物质地更接近成人，但仍需注意安全',
          tips: '培养良好的用餐礼仪，使用餐具进食'
        }
      },
      milestones: {
        physical: ['可能开始独立行走', '能蹲下再站起', '攀爬能力强'],
        cognitive: ['问题解决能力提高', '能完成两步指令', '分类概念萌芽'],
        language: ['说出2-3个有意义的词', '理解更多指令', '开始模仿句子语调'],
        social: ['喜欢模仿家务活动', '开始有帮助意识', '对其他孩子更感兴趣'],
        emotional: ['自我意识增强', '开始有羞耻感', '情绪调节能力提高'],
        motor: ['精细动作接近成熟', '能翻书页', '开始有涂鸦行为']
      }
    },

    12: { // 12个月 - 幼儿期开始
      monthAge: 12,
      title: '12个月',
      careRequirements: {
        skinCare: '注意保湿，特别是秋冬季节。选择温和的婴幼儿专用护肤品。',
        oralCare: '开始使用婴幼儿牙刷，每天清洁2次。可以开始使用含氟牙膏（米粒大小）。',
        bathingCare: '可以站立洗澡，但仍需监护。培养洗澡时的自理能力。',
        sleepCare: '夜间睡眠10-12小时，白天1-2次小睡。建立固定的睡前仪式。',
        exerciseCare: '鼓励站立、行走。提供安全的探索环境。',
        emotionalCare: '理解并回应宝宝的情感表达。建立安全感和信任感。',
        generalCare: '可以吃更多种类的食物。注意安全防护，防止意外伤害。'
      },
      feeding: {
        breastfeeding: {
          frequency: '每天2-4次，主要在睡前和夜间',
          duration: '每次10-15分钟',
          tips: '可以考虑断奶，但要循序渐进。',
          problems: ['断奶困难', '营养不均衡']
        },
        complementaryFood: {
          introduced: true,
          foods: ['软饭', '面条', '肉类', '鱼类', '豆类', '各种蔬果'],
          forbidden: ['蜂蜜仍需谨慎', '整颗坚果', '硬糖'],
          schedule: '一日三餐加1-2次点心',
          preparation: '食物可以有小颗粒，锻炼咀嚼能力',
          tips: '鼓励自主进食，提供手指食物'
        },
        water: {
          needed: true,
          amount: '每天200-300ml',
          tips: '可以用杯子喝水，培养良好的饮水习惯'
        }
      },
      milestones: {
        physical: ['能独立站立', '开始学步', '有6-8颗牙齿'],
        cognitive: ['理解简单指令', '有物体永恒概念', '开始模仿行为'],
        language: ['说出第一个有意义的词', '理解常用词汇'],
        social: ['喜欢与其他孩子玩耍', '开始有分享意识'],
        emotional: ['表现出同理心', '有明显的个性特征'],
        motor: ['精细动作发展', '能用拇指和食指捏取小物品']
      },
      faqs: [
        {
          question: '1岁宝宝还需要喝奶吗？',
          answer: '是的，奶类仍是重要营养来源。可以是母乳、配方奶或全脂牛奶。每天建议500-600ml。',
          tips: '如果断母乳，可以逐渐过渡到配方奶或全脂牛奶。',
          category: '喂养'
        },
        {
          question: '宝宝什么时候开始学走路？',
          answer: '大多数宝宝在9-18个月之间学会走路。12个月左右开始尝试是正常的。',
          tips: '不要急于使用学步车，让宝宝自然发展更好。',
          category: '发育'
        }
      ]
    },

    13: { // 13个月
      monthAge: 13,
      title: '13个月',
      careRequirements: {
        skinCare: '学步期注意膝盖和手部保护，选择合适的学步鞋。保持皮肤清洁干燥。',
        oralCare: '继续建立刷牙习惯，每天早晚各一次。可以让宝宝模仿刷牙动作。',
        bathingCare: '可以站立洗澡，增加洗澡的趣味性。注意防滑和水温。',
        sleepCare: '夜间睡眠11-14小时，白天1-2次小睡。建立稳定的作息时间。',
        exerciseCare: '鼓励独立行走，提供安全的练习空间。避免过度使用学步车。',
        emotionalCare: '理解宝宝的情绪表达，给予适当的安慰和鼓励。',
        generalCare: '注意家居安全，为活跃的学步儿做好防护措施。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['软饭', '面条', '蒸蛋', '软质肉类', '各种蔬果', '全脂牛奶'],
          forbidden: ['蜂蜜仍需谨慎', '坚果', '硬糖', '高盐高糖食物'],
          schedule: '一日三餐加1-2次健康零食',
          preparation: '食物质地接近成人，但仍需注意大小和软硬度',
          tips: '鼓励使用餐具，培养良好的用餐习惯'
        }
      },
      milestones: {
        physical: ['独立行走几步', '能蹲下捡东西', '开始跑步的尝试'],
        cognitive: ['能完成简单的形状配对', '理解物品的用途', '记忆力显著提高'],
        language: ['词汇量增加到5-10个', '开始模仿新词', '理解简单指令'],
        social: ['喜欢模仿大人行为', '开始有分享意识', '对其他孩子感兴趣'],
        emotional: ['表现出独立性', '开始有挫折感', '对表扬有积极反应'],
        motor: ['精细动作发展', '能翻书页', '开始有涂鸦行为']
      }
    },

    15: { // 15个月
      monthAge: 15,
      title: '15个月',
      careRequirements: {
        skinCare: '活动量增大，注意皮肤保护。选择透气舒适的衣物。',
        oralCare: '可能有6-8颗牙齿，加强口腔清洁。使用含氟牙膏（米粒大小）。',
        bathingCare: '洗澡时可以增加教育性游戏，如认识身体部位、颜色等。',
        sleepCare: '夜间睡眠稳定，白天可能只需要1次小睡。',
        exerciseCare: '鼓励更多的大运动，如爬楼梯、踢球等。',
        emotionalCare: '开始建立规则意识，温和而坚定地设定界限。',
        generalCare: '语言发展关键期，多与宝宝交流，读书讲故事。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['家庭餐食', '更多种类的蛋白质', '全谷物', '奶制品'],
          forbidden: ['蜂蜜', '坚果', '硬糖', '高盐高糖食物', '生鸡蛋'],
          schedule: '与家庭用餐时间同步，三餐两点',
          preparation: '食物切成适当大小，避免窒息风险',
          tips: '鼓励自主进食，允许一定程度的"混乱"'
        }
      },
      milestones: {
        physical: ['独立行走稳定', '能推拉玩具', '开始尝试跑步'],
        cognitive: ['能指认身体部位', '理解"里面"和"上面"', '开始有因果思维'],
        language: ['词汇量10-20个', '开始组合词语', '理解更多指令'],
        social: ['喜欢与大人互动', '开始模仿家务活动', '对规则有初步理解'],
        emotional: ['自我意识增强', '开始有同理心', '情绪表达更丰富'],
        motor: ['能搭2-3块积木', '开始使用勺子', '精细动作更协调']
      }
    },

    18: { // 18个月
      monthAge: 18,
      title: '18个月',
      careRequirements: {
        skinCare: '户外活动增多，注意防晒和皮肤保护。及时清洁汗渍。',
        oralCare: '建立规律的刷牙习惯，可以开始使用电动牙刷。',
        bathingCare: '可以尝试淋浴，但仍需要帮助。增加水中教育游戏。',
        sleepCare: '大多数宝宝只需要一次午睡，夜间睡眠11-14小时。',
        exerciseCare: '鼓励各种大运动，如跑步、跳跃、爬楼梯等。',
        emotionalCare: '理解并接受宝宝的情绪波动，教导情绪表达方式。',
        generalCare: '语言爆发期，创造丰富的语言环境。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['成人食物', '各种质地的食物', '手指食物', '健康零食'],
          forbidden: ['蜂蜜', '整颗坚果', '硬糖', '高盐高糖食物'],
          schedule: '完全融入家庭用餐',
          preparation: '食物大小适中，鼓励咀嚼',
          tips: '培养良好的用餐礼仪，使用餐具进食'
        }
      },
      milestones: {
        physical: ['能跑步', '能踢球', '能爬上椅子'],
        cognitive: ['能完成简单拼图', '理解物品分类', '记忆力强'],
        language: ['词汇量20-50个', '开始说两词句', '能指认图片中的物品'],
        social: ['喜欢与其他孩子玩耍', '开始有合作游戏', '模仿能力强'],
        emotional: ['自主性增强', '开始有羞耻感', '对挫折的反应强烈'],
        motor: ['能搭4-6块积木', '开始涂鸦', '能翻书页']
      }
    },

    21: { // 21个月
      monthAge: 21,
      title: '21个月',
      careRequirements: {
        skinCare: '注意季节性皮肤护理，保持适当的湿润度。',
        oralCare: '可能有12-16颗牙齿，加强清洁。开始使用牙线。',
        bathingCare: '可以独立站立洗澡，但仍需监督。培养自理能力。',
        sleepCare: '睡眠时间稳定，可能出现短暂的睡眠倒退。',
        exerciseCare: '鼓励更复杂的运动，如跳跃、平衡等。',
        emotionalCare: '帮助宝宝学会表达情感，处理挫折感。',
        generalCare: '为2岁生日做准备，关注行为管理。'
      },
      milestones: {
        physical: ['能双脚跳', '能踢球给别人', '能走楼梯（扶栏杆）'],
        cognitive: ['能完成更复杂的任务', '理解时间概念', '分类能力发展'],
        language: ['词汇量50-100个', '开始说三词句', '能回答简单问题'],
        social: ['喜欢角色扮演', '开始有同伴意识', '能遵守简单规则'],
        emotional: ['情绪调节能力提高', '开始有羞耻感', '自我意识强'],
        motor: ['精细动作发展', '能画圆圈', '能使用剪刀（在指导下）']
      }
    },

    24: { // 24个月 - 2岁
      monthAge: 24,
      title: '24个月（2岁）',
      careRequirements: {
        skinCare: '继续日常护理，注意户外活动后的清洁。选择适合的护肤品。',
        oralCare: '建立完整的口腔护理习惯，定期看牙医。',
        bathingCare: '可以参与洗澡过程，学习自己洗手洗脸。',
        sleepCare: '夜间睡眠11-14小时，午睡1-3小时。',
        exerciseCare: '鼓励各种体育活动，发展协调性和平衡感。',
        emotionalCare: '理解"可怕的两岁"，耐心处理情绪爆发。',
        generalCare: '开始如厕训练的准备，观察准备信号。'
      },
      milestones: {
        physical: ['能跑得很稳', '能双脚跳离地面', '能踢球'],
        cognitive: ['能完成简单拼图', '理解大小概念', '记忆力强'],
        language: ['词汇量150-300个', '能说简单句子', '开始问"为什么"'],
        social: ['平行游戏为主', '开始有分享概念', '模仿大人行为'],
        emotional: ['自我意识强', '开始有独立性', '情绪波动大'],
        motor: ['能搭6-8块积木', '开始画线条', '能使用勺子和叉子']
      }
    },

    27: { // 27个月
      monthAge: 27,
      title: '27个月',
      careRequirements: {
        skinCare: '注意户外活动的防护，使用儿童专用防晒霜。保持皮肤清洁。',
        oralCare: '大部分乳牙已萌出，建立完整的刷牙习惯。定期看牙医。',
        bathingCare: '可以独立完成部分洗澡步骤，培养自理能力。',
        sleepCare: '夜间睡眠11-13小时，午睡1-2小时。可能出现睡眠抗拒。',
        exerciseCare: '鼓励各种运动技能，如骑三轮车、爬攀登架等。',
        emotionalCare: '帮助处理复杂情绪，教导情绪表达和调节方法。',
        generalCare: '如厕训练进行中，保持耐心和一致性。'
      },
      milestones: {
        physical: ['能单脚站立片刻', '能踢球给特定方向', '能爬攀登架'],
        cognitive: ['能完成3-4片拼图', '理解数字1-3', '记忆力显著提高'],
        language: ['词汇量300-500个', '能说完整句子', '开始问很多问题'],
        social: ['开始合作游戏', '能轮流等待', '对其他孩子更感兴趣'],
        emotional: ['情绪表达更准确', '开始理解他人感受', '自控能力发展'],
        motor: ['能画圆形', '能使用剪刀剪纸', '能搭高塔（8-10块）']
      }
    },

    30: { // 30个月
      monthAge: 30,
      title: '30个月',
      careRequirements: {
        skinCare: '继续日常护理，注意季节变化对皮肤的影响。',
        oralCare: '20颗乳牙基本萌出完毕，加强口腔护理。',
        bathingCare: '基本能独立洗澡，但仍需监督安全。',
        sleepCare: '睡眠模式稳定，为幼儿园作息做准备。',
        exerciseCare: '发展更复杂的运动技能，如跳跃、平衡等。',
        emotionalCare: '处理分离焦虑，为入园做心理准备。',
        generalCare: '如厕训练基本完成，开始夜间训练。'
      },
      milestones: {
        physical: ['能双脚交替上楼梯', '能骑三轮车', '能接住大球'],
        cognitive: ['能完成简单的逻辑推理', '理解时间概念', '分类能力强'],
        language: ['词汇量500-1000个', '能讲简单故事', '语法基本正确'],
        social: ['喜欢群体游戏', '能遵守游戏规则', '开始有友谊概念'],
        emotional: ['情绪调节能力提高', '开始有羞耻感和骄傲感', '自我意识强'],
        motor: ['能画人物（头和四肢）', '能使用筷子', '精细动作协调']
      }
    },

    33: { // 33个月
      monthAge: 33,
      title: '33个月',
      careRequirements: {
        skinCare: '注意保护皮肤，特别是户外活动时的防晒。',
        oralCare: '建立完整的口腔护理习惯，教导正确的刷牙方法。',
        bathingCare: '能独立完成大部分洗澡步骤，培养自理能力。',
        sleepCare: '为幼儿园作息做准备，调整睡眠时间。',
        exerciseCare: '鼓励各种体育活动，发展协调性和团队合作。',
        emotionalCare: '帮助建立自信心，处理挫折和失败。',
        generalCare: '为幼儿园入学做全面准备，包括生活技能和社交技能。'
      },
      milestones: {
        physical: ['能单脚跳', '能踢球给队友', '能骑平衡车'],
        cognitive: ['能完成复杂拼图', '理解数字概念', '逻辑思维发展'],
        language: ['词汇量1000+个', '能进行对话', '开始学习字母'],
        social: ['能与同伴合作', '理解分享和轮流', '有同理心'],
        emotional: ['情绪表达准确', '能自我安慰', '有责任感'],
        motor: ['能画复杂图形', '能使用各种工具', '手眼协调良好']
      }
    },

    36: { // 36个月 - 3岁
      monthAge: 36,
      title: '36个月（3岁）',
      careRequirements: {
        skinCare: '建立独立的个人卫生习惯，教导自己洗手洗脸。',
        oralCare: '能独立刷牙，但仍需监督。定期看牙医检查。',
        bathingCare: '基本能独立洗澡，注意安全监督。',
        sleepCare: '夜间睡眠10-12小时，可能不再需要午睡。',
        exerciseCare: '参与各种体育活动，发展运动技能和团队精神。',
        emotionalCare: '帮助建立自信心和独立性，准备进入学前教育。',
        generalCare: '全面的学前准备，包括认知、社交、情感和生活技能。'
      },
      feeding: {
        complementaryFood: {
          introduced: true,
          foods: ['完全成人化饮食', '各种营养均衡的食物'],
          forbidden: ['仍需注意窒息风险食物', '限制高糖高盐食物'],
          schedule: '规律的三餐两点，与成人同步',
          preparation: '鼓励参与食物准备，学习营养知识',
          tips: '建立良好的饮食习惯，培养不挑食的态度'
        }
      },
      milestones: {
        physical: ['能跑跳自如', '能骑自行车（有辅助轮）', '能参与体育游戏'],
        cognitive: ['能完成复杂任务', '理解抽象概念', '准备学习读写'],
        language: ['词汇量2000+个', '能流利对话', '开始学习读写'],
        social: ['能与同伴友好相处', '理解社会规则', '有合作精神'],
        emotional: ['情绪管理能力强', '有自信心', '能处理挫折'],
        motor: ['精细动作成熟', '能画复杂图画', '准备学习写字']
      },
      faqs: [
        {
          question: '3岁宝宝应该具备哪些能力？',
          answer: '3岁宝宝应该能独立行走跑跳、说完整句子、与人交流、基本的自理能力、遵守简单规则。',
          tips: '每个孩子发展速度不同，不要过分比较，关注孩子的整体发展。',
          category: '发育'
        },
        {
          question: '如何为幼儿园做准备？',
          answer: '培养独立性、社交技能、情绪管理能力、基本的自理技能、适应集体生活的能力。',
          tips: '提前适应幼儿园作息，多与其他孩子接触，培养分离适应能力。',
          category: '教育'
        }
      ]
    }
  }
}

/**
 * 获取特定月龄的百科日记
 * @param {number} monthAge - 月龄 (0-36)
 * @returns {Object|null} 百科日记数据
 */
function getEncyclopediaDiary(monthAge) {
  try {
    const encyclopediaData = getEncyclopediaData()
    return encyclopediaData[monthAge] || null
  } catch (error) {
    console.error('获取百科日记失败:', error)
    return null
  }
}

/**
 * 获取月龄范围的百科日记列表
 * @param {number} startMonth - 开始月龄
 * @param {number} endMonth - 结束月龄
 * @returns {Array} 百科日记列表
 */
function getEncyclopediaDiariesByRange(startMonth, endMonth) {
  try {
    const encyclopediaData = getEncyclopediaData()
    const diaries = []
    
    for (let month = startMonth; month <= endMonth; month++) {
      if (encyclopediaData[month]) {
        diaries.push(encyclopediaData[month])
      }
    }
    
    return diaries
  } catch (error) {
    console.error('获取百科日记列表失败:', error)
    return []
  }
}

/**
 * 搜索百科日记内容
 * @param {string} keyword - 搜索关键词
 * @returns {Array} 搜索结果
 */
function searchEncyclopediaDiary(keyword) {
  try {
    if (!keyword || keyword.trim() === '') {
      return []
    }
    
    const encyclopediaData = getEncyclopediaData()
    const results = []
    const searchTerm = keyword.toLowerCase()
    
    Object.values(encyclopediaData).forEach(diary => {
      const matches = []
      
      // 搜索标题
      if (diary.title && diary.title.toLowerCase().includes(searchTerm)) {
        matches.push({
          type: 'title',
          content: diary.title,
          monthAge: diary.monthAge
        })
      }

      // 搜索FAQ
      if (diary.faqs && Array.isArray(diary.faqs)) {
        diary.faqs.forEach(faq => {
          if (faq.question.toLowerCase().includes(searchTerm) ||
              faq.answer.toLowerCase().includes(searchTerm)) {
            matches.push({
              type: 'faq',
              content: faq.question,
              answer: faq.answer,
              monthAge: diary.monthAge
            })
          }
        })
      }
      
      // 搜索护理要求
      if (diary.careRequirements) {
        Object.entries(diary.careRequirements).forEach(([key, value]) => {
          if (value && value.toLowerCase().includes(searchTerm)) {
            matches.push({
              type: 'care',
              content: `${key}: ${value}`,
              monthAge: diary.monthAge
            })
          }
        })
      }

      // 搜索注意事项
      if (diary.precautions) {
        Object.entries(diary.precautions).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            value.forEach(item => {
              if (item && item.toLowerCase().includes(searchTerm)) {
                matches.push({
                  type: 'precaution',
                  content: `${key}: ${item}`,
                  monthAge: diary.monthAge
                })
              }
            })
          } else if (value && value.toLowerCase().includes(searchTerm)) {
            matches.push({
              type: 'precaution',
              content: `${key}: ${value}`,
              monthAge: diary.monthAge
            })
          }
        })
      }

      // 搜索发育里程碑
      if (diary.milestones) {
        Object.entries(diary.milestones).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            value.forEach(item => {
              if (item && item.toLowerCase().includes(searchTerm)) {
                matches.push({
                  type: 'milestone',
                  content: `${key}: ${item}`,
                  monthAge: diary.monthAge
                })
              }
            })
          }
        })
      }

      // 搜索喂养指导
      if (diary.feeding) {
        const searchFeeding = (obj, prefix = '') => {
          Object.entries(obj).forEach(([key, value]) => {
            if (typeof value === 'string' && value.toLowerCase().includes(searchTerm)) {
              matches.push({
                type: 'feeding',
                content: `${prefix}${key}: ${value}`,
                monthAge: diary.monthAge
              })
            } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              searchFeeding(value, `${prefix}${key}.`)
            } else if (Array.isArray(value)) {
              value.forEach(item => {
                if (typeof item === 'string' && item.toLowerCase().includes(searchTerm)) {
                  matches.push({
                    type: 'feeding',
                    content: `${prefix}${key}: ${item}`,
                    monthAge: diary.monthAge
                  })
                }
              })
            }
          })
        }
        searchFeeding(diary.feeding)
      }
      
      if (matches.length > 0) {
        results.push({
          monthAge: diary.monthAge,
          title: diary.title,
          matches: matches
        })
      }
    })
    
    return results
  } catch (error) {
    console.error('搜索百科日记失败:', error)
    return []
  }
}

/**
 * 获取月龄标题
 * @param {number} monthAge - 月龄
 * @returns {string} 格式化的月龄标题
 */
function getMonthAgeTitle(monthAge) {
  if (monthAge === 0) {
    return '新生儿期（0-1个月）'
  } else if (monthAge <= 12) {
    return `${monthAge}个月`
  } else {
    const years = Math.floor(monthAge / 12)
    const months = monthAge % 12
    if (months === 0) {
      return `${years}岁`
    } else {
      return `${years}岁${months}个月`
    }
  }
}

/**
 * 获取月龄分组
 * @returns {Array} 月龄分组列表
 */
function getMonthAgeGroups() {
  return [
    { id: 'newborn', name: '新生儿期', range: [0, 1], icon: '👶' },
    { id: 'infant', name: '婴儿期', range: [1, 6], icon: '🍼' },
    { id: 'mobile', name: '活动期', range: [6, 12], icon: '🚼' },
    { id: 'toddler', name: '幼儿期', range: [12, 24], icon: '🧒' },
    { id: 'preschool', name: '学前期', range: [24, 36], icon: '👦' }
  ]
}

module.exports = {
  EncyclopediaDiaryModel,
  getEncyclopediaData,
  getEncyclopediaDiary,
  getEncyclopediaDiariesByRange,
  searchEncyclopediaDiary,
  getMonthAgeTitle,
  getMonthAgeGroups
}
