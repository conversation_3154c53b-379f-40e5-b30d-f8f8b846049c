// 成长日记数据模型和工具函数
const app = getApp()

/**
 * 成长日记数据模型
 */
const GrowthDiaryModel = {
  // 基础信息
  id: '', // 唯一标识符
  childId: '', // 关联的孩子ID
  year: 0, // 年份
  month: 0, // 月份 (1-12)
  createTime: '', // 创建时间
  updateTime: '', // 更新时间
  
  // 呵护要求
  careRequirements: {
    skinCare: '', // 皮肤护理
    oralCare: '', // 口腔护理
    bathingCare: '', // 洗澡护理
    sleepCare: '', // 睡眠护理
    exerciseCare: '', // 运动护理
    emotionalCare: '', // 情感呵护
    notes: '' // 其他呵护要求
  },
  
  // 注意事项
  precautions: {
    safety: [], // 安全注意事项
    health: [], // 健康注意事项
    development: [], // 发育注意事项
    environment: [], // 环境注意事项
    interaction: [], // 互动注意事项
    customNotes: '' // 自定义注意事项
  },
  
  // 饮食记录
  feeding: {
    breastfeeding: {
      frequency: 0, // 母乳喂养次数/天
      duration: 0, // 每次喂养时长(分钟)
      notes: '' // 备注
    },
    formulaFeeding: {
      frequency: 0, // 奶粉喂养次数/天
      amount: 0, // 每次奶量(ml)
      brand: '', // 奶粉品牌
      notes: '' // 备注
    },
    complementaryFood: {
      introduced: false, // 是否开始添加辅食
      foods: [], // 已添加的食物列表
      allergies: [], // 过敏食物
      schedule: '', // 喂养时间安排
      notes: '' // 备注
    },
    water: {
      amount: 0, // 每日饮水量(ml)
      notes: '' // 备注
    },
    supplements: {
      vitaminD: false, // 是否补充维生素D
      iron: false, // 是否补充铁剂
      calcium: false, // 是否补充钙剂
      others: [], // 其他补充剂
      notes: '' // 备注
    }
  },
  
  // 排尿记录
  urination: {
    frequency: 0, // 每日排尿次数
    color: '', // 尿液颜色 (淡黄/深黄/其他)
    smell: '', // 气味 (正常/异常)
    diaperChanges: 0, // 每日换尿布次数
    accidents: 0, // 意外次数(如漏尿)
    notes: '' // 备注
  },
  
  // 排便记录
  defecation: {
    frequency: 0, // 每日排便次数
    consistency: '', // 便便性状 (软便/成形/硬便/水样)
    color: '', // 颜色 (黄色/绿色/棕色/其他)
    smell: '', // 气味 (正常/异常)
    difficulty: false, // 是否排便困难
    blood: false, // 是否有血丝
    notes: '' // 备注
  },
  
  // 衣服穿着
  clothing: {
    dailyOutfits: [], // 每日穿着记录
    seasonalClothing: {
      spring: [], // 春季衣物
      summer: [], // 夏季衣物
      autumn: [], // 秋季衣物
      winter: [] // 冬季衣物
    },
    sizes: {
      tops: '', // 上衣尺码
      bottoms: '', // 下装尺码
      shoes: '', // 鞋子尺码
      hats: '', // 帽子尺码
      socks: '' // 袜子尺码
    },
    materials: [], // 偏好材质
    specialNeeds: '', // 特殊需求(如过敏材质)
    notes: '' // 备注
  },
  
  // 发育里程碑
  milestones: {
    physical: [], // 身体发育里程碑
    cognitive: [], // 认知发育里程碑
    language: [], // 语言发育里程碑
    social: [], // 社交发育里程碑
    emotional: [], // 情感发育里程碑
    motor: [] // 运动发育里程碑
  },
  
  // 健康记录
  health: {
    weight: 0, // 体重(kg)
    height: 0, // 身高(cm)
    headCircumference: 0, // 头围(cm)
    temperature: 0, // 体温(°C)
    vaccinations: [], // 疫苗接种记录
    illnesses: [], // 疾病记录
    medications: [], // 用药记录
    doctorVisits: [], // 医生检查记录
    notes: '' // 健康备注
  },
  
  // 睡眠记录
  sleep: {
    nightSleep: {
      bedtime: '', // 晚上入睡时间
      wakeupTime: '', // 早上起床时间
      duration: 0, // 夜间睡眠时长(小时)
      nightWakings: 0, // 夜间醒来次数
      quality: '' // 睡眠质量 (好/一般/差)
    },
    napSleep: {
      frequency: 0, // 白天小睡次数
      totalDuration: 0, // 白天小睡总时长(小时)
      schedule: [] // 小睡时间安排
    },
    sleepEnvironment: {
      temperature: 0, // 室温(°C)
      humidity: 0, // 湿度(%)
      lighting: '', // 光线条件
      noise: '', // 噪音情况
      bedding: '' // 床上用品
    },
    notes: '' // 睡眠备注
  },
  
  // 活动记录
  activities: {
    indoor: [], // 室内活动
    outdoor: [], // 户外活动
    educational: [], // 教育活动
    social: [], // 社交活动
    creative: [], // 创意活动
    physical: [], // 体能活动
    notes: '' // 活动备注
  },
  
  // 照片记录
  photos: {
    daily: [], // 日常照片
    milestones: [], // 里程碑照片
    activities: [], // 活动照片
    growth: [], // 成长对比照片
    notes: '' // 照片备注
  },
  
  // 家长心得
  parentNotes: {
    observations: '', // 观察记录
    concerns: '', // 担心的问题
    achievements: '', // 成就记录
    challenges: '', // 遇到的挑战
    tips: '', // 育儿心得
    questions: '', // 想咨询的问题
    mood: '', // 家长心情
    gratitude: '' // 感恩记录
  }
}

/**
 * 创建新的成长日记记录
 * @param {string} childId - 孩子ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {Object} 新的成长日记记录
 */
function createGrowthDiary(childId, year, month) {
  const now = new Date()
  const id = `diary_${childId}_${year}_${month}_${now.getTime()}`
  
  return Object.assign({}, GrowthDiaryModel, {
    id,
    childId,
    year,
    month,
    createTime: now.toISOString(),
    updateTime: now.toISOString()
  })
}

/**
 * 获取成长日记存储键
 * @param {string} childId - 孩子ID
 * @returns {string} 存储键
 */
function getStorageKey(childId) {
  return `growth_diary_${childId}`
}

/**
 * 保存成长日记
 * @param {Object} diary - 成长日记数据
 * @returns {boolean} 保存是否成功
 */
function saveGrowthDiary(diary) {
  try {
    const storageKey = getStorageKey(diary.childId)
    let diaries = wx.getStorageSync(storageKey) || []
    
    // 查找是否已存在相同月份的记录
    const existingIndex = diaries.findIndex(d => 
      d.year === diary.year && d.month === diary.month
    )
    
    diary.updateTime = new Date().toISOString()
    
    if (existingIndex >= 0) {
      // 更新现有记录
      diaries[existingIndex] = diary
    } else {
      // 添加新记录
      diaries.push(diary)
    }
    
    // 按年月排序
    diaries.sort((a, b) => {
      if (a.year !== b.year) return b.year - a.year
      return b.month - a.month
    })
    
    wx.setStorageSync(storageKey, diaries)
    return true
  } catch (error) {
    console.error('保存成长日记失败:', error)
    return false
  }
}

/**
 * 获取成长日记列表
 * @param {string} childId - 孩子ID
 * @returns {Array} 成长日记列表
 */
function getGrowthDiaries(childId) {
  try {
    const storageKey = getStorageKey(childId)
    return wx.getStorageSync(storageKey) || []
  } catch (error) {
    console.error('获取成长日记失败:', error)
    return []
  }
}

/**
 * 获取特定月份的成长日记
 * @param {string} childId - 孩子ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {Object|null} 成长日记记录
 */
function getGrowthDiary(childId, year, month) {
  try {
    const diaries = getGrowthDiaries(childId)
    return diaries.find(d => d.year === year && d.month === month) || null
  } catch (error) {
    console.error('获取成长日记失败:', error)
    return null
  }
}

/**
 * 删除成长日记
 * @param {string} childId - 孩子ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {boolean} 删除是否成功
 */
function deleteGrowthDiary(childId, year, month) {
  try {
    const storageKey = getStorageKey(childId)
    let diaries = getGrowthDiaries(childId)
    
    diaries = diaries.filter(d => !(d.year === year && d.month === month))
    
    wx.setStorageSync(storageKey, diaries)
    return true
  } catch (error) {
    console.error('删除成长日记失败:', error)
    return false
  }
}

/**
 * 格式化月份显示
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {string} 格式化的月份字符串
 */
function formatMonth(year, month) {
  return `${year}年${month}月`
}

/**
 * 获取月份的天数
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {number} 天数
 */
function getDaysInMonth(year, month) {
  return new Date(year, month, 0).getDate()
}

/**
 * 验证成长日记数据
 * @param {Object} diary - 成长日记数据
 * @returns {Object} 验证结果
 */
function validateGrowthDiary(diary) {
  const errors = []
  
  if (!diary.childId) {
    errors.push('缺少孩子ID')
  }
  
  if (!diary.year || diary.year < 2020 || diary.year > 2030) {
    errors.push('年份无效')
  }
  
  if (!diary.month || diary.month < 1 || diary.month > 12) {
    errors.push('月份无效')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

module.exports = {
  GrowthDiaryModel,
  createGrowthDiary,
  saveGrowthDiary,
  getGrowthDiaries,
  getGrowthDiary,
  deleteGrowthDiary,
  formatMonth,
  getDaysInMonth,
  validateGrowthDiary
}
