/* encyclopedia-detail.wxss - 百科详情页面样式 */

/* 引入全局样式变量 */
@import "../../app.wxss";

/* 页面容器 */
.page {
  background: #f7f8fa;
}

/* 主内容区 */
.main-content {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

/* 信息卡片 */
.info-card {
  margin: 20rpx 24rpx 32rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}



/* 卡片内容 */
.card-body {
  padding: 32rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32rpx;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.meta-label {
  font-size: 26rpx;
  color: #666;
}

.meta-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}





/* 内容区域 */
.content-area {
  padding: 0 28rpx 160rpx;
}

.content-section {
  margin-bottom: 24rpx;
}

.modern-card {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.care-icon {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a80 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.3);
}

.warning-icon {
  background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 167, 38, 0.3);
}

.section-emoji {
  font-size: 36rpx !important;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.section-desc {
  font-size: 24rpx;
  color: #666;
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.toggle-button.expanded {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

.toggle-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.toggle-button.expanded .toggle-text {
  color: white;
}

.toggle-arrow {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

.toggle-button.expanded .toggle-arrow {
  color: white;
}

/* 导航标签 */
.nav-tabs {
  display: flex;
  gap: 16rpx;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-tabs::-webkit-scrollbar {
  display: none;
}

.nav-tab {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 22rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.nav-tab.active {
  background: rgba(255,255,255,0.3);
}

/* 内容区域 */
.content {
  padding: 32rpx;
  margin-top: -32rpx;
  position: relative;
  z-index: 3;
}

/* 章节卡片 */
.section-card {
  background: white;
  border-radius: 40rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 64rpx rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.8);
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 32rpx 36rpx;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.section-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(79, 70, 229, 0.3);
}

.section-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1e293b;
}

.toggle-icon {
  font-size: 24rpx;
  color: #64748b;
}

.section-content {
  padding: 36rpx;
  background: white;
}

/* 信息项 */
.info-item {
  display: flex;
  margin-bottom: 28rpx;
  align-items: flex-start;
  padding: 20rpx;
  background: rgba(79, 70, 229, 0.02);
  border-radius: 12rpx;
  border-left: 4rpx solid var(--primary-color);
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 600;
  min-width: 180rpx;
  flex-shrink: 0;
  margin-right: 16rpx;
}

.info-value {
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.7;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

/* 子章节 */
.subsection {
  margin-bottom: 36rpx;
  padding: 28rpx;
  background: linear-gradient(135deg, var(--bg-light) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 16rpx;
  border: 1px solid var(--border-light);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsection-title {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid var(--primary-color);
  position: relative;
}

.subsection-title::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  background: var(--primary-color);
  border-radius: 50%;
  margin-right: 12rpx;
}

/* 列表样式 */
.list-section {
  margin-bottom: 24rpx;
}

.list-section:last-child {
  margin-bottom: 0;
}

.list-title {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.list-item {
  margin-bottom: 8rpx;
  padding-left: 16rpx;
}

.list-item:last-child {
  margin-bottom: 0;
}

.list-item text {
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.6;
}

.list-item.emergency text {
  color: #ef4444;
  font-weight: 500;
}

/* 里程碑网格 */
.milestone-grid {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.milestone-category {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
  border-radius: 16rpx;
  padding: 28rpx;
  border: 1px solid rgba(79, 70, 229, 0.1);
  box-shadow: 0 2rpx 12rpx rgba(79, 70, 229, 0.08);
}

.milestone-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid rgba(79, 70, 229, 0.2);
}

.milestone-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.milestone-title {
  font-size: 30rpx;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

.milestone-item {
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.6;
  padding: 8rpx 0;
  display: flex;
  align-items: flex-start;
}

.milestone-item:last-child {
  margin-bottom: 0;
}

.milestone-item::before {
  content: '✓';
  color: #10b981;
  font-weight: bold;
  margin-right: 12rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

/* FAQ样式 */
.faq-item {
  margin-bottom: 36rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, var(--bg-light) 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1px solid var(--border-light);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-question {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid rgba(79, 70, 229, 0.1);
}

.question-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color) 0%, #6366f1 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(79, 70, 229, 0.3);
}

.question-text {
  font-size: 30rpx;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.6;
  letter-spacing: 0.5rpx;
}

.faq-answer {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.answer-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}

.answer-text {
  font-size: 28rpx;
  color: var(--text-primary);
  line-height: 1.7;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

.faq-tips {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.08) 0%, rgba(79, 70, 229, 0.04) 100%);
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid var(--primary-color);
}

.tips-icon {
  font-size: 28rpx;
  flex-shrink: 0;
}

.tips-text {
  font-size: 26rpx;
  color: var(--primary-color);
  line-height: 1.6;
  font-weight: 500;
}

/* 加载和空状态 */
.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.loading-icon, .empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.loading-text, .empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}



/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20rpx;
    min-height: auto;
  }

  .title-section .card-title {
    font-size: 32rpx;
  }

  .header-actions {
    justify-content: flex-end;
    margin-top: 0;
  }

  .btn-icon {
    width: 48rpx;
    height: 48rpx;
    font-size: 24rpx;
  }

  .btn-secondary {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
  }

  .info-item {
    flex-direction: column;
    gap: 12rpx;
    padding: 16rpx;
  }

  .info-label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 8rpx;
  }



  .section-content {
    padding: 28rpx 24rpx;
  }

  .subsection {
    padding: 24rpx 20rpx;
  }

  .faq-item {
    padding: 28rpx 24rpx;
  }
}

/* 卡片内容 */
.card-body {
  padding: 32rpx;
}

/* 呵护要求样式 */
.care-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.care-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 20rpx;
  border-left: 6rpx solid #667eea;
  transition: all 0.3s ease;
}

.care-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
}

.item-icon {
  width: 64rpx;
  height: 64rpx;
  background: white;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.item-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 注意事项样式 */
.precaution-groups {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.precaution-group {
  background: #f8f9fa;
  border-radius: 20rpx;
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.group-icon {
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.alert-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.alert-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-top: 8rpx;
  flex-shrink: 0;
}

.alert-item.safety .alert-dot {
  background: #4caf50;
}

.alert-item.health .alert-dot {
  background: #2196f3;
}

.alert-item.emergency .alert-dot {
  background: #f44336;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.alert-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}

/* 饮食指导样式 */
.feeding-icon {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  box-shadow: 0 8rpx 24rpx rgba(79, 195, 247, 0.3);
}

.feeding-sections {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.feeding-section {
  background: linear-gradient(135deg, #f8fdff 0%, #e3f2fd 100%);
  border-radius: 20rpx;
  overflow: hidden;
  border: 2rpx solid #e1f5fe;
}

.feeding-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-bottom: 1rpx solid #e1f5fe;
}

.feeding-section .feeding-icon {
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.feeding-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-left: 16rpx;
}

.feeding-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.feeding-badge.recommended {
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  color: white;
}

.feeding-badge.alternative {
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
  color: white;
}

.feeding-badge.supplement {
  background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
  color: white;
}

.feeding-stats {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.feeding-tips {
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.7);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.supplement-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.supplement-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.supplement-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #e1bee7 0%, #ce93d8 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.supplement-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.supplement-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.supplement-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 发育里程碑样式 */
.milestone-icon {
  background: linear-gradient(135deg, #8bc34a 0%, #4caf50 100%);
  box-shadow: 0 8rpx 24rpx rgba(139, 195, 74, 0.3);
}

.milestone-categories {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.milestone-category {
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
  border-radius: 20rpx;
  overflow: hidden;
  border: 2rpx solid #c8e6c9;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-bottom: 1rpx solid #a5d6a7;
}

.category-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.category-icon.physical {
  background: linear-gradient(135deg, #f44336 0%, #e57373 100%);
}

.category-icon.cognitive {
  background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
}

.category-icon.language {
  background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);
}

.category-icon.social {
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
}

.category-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.category-desc {
  font-size: 24rpx;
  color: #666;
}

.progress-indicator {
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #a5d6a7;
}

.progress-text {
  font-size: 22rpx;
  color: #4caf50;
  font-weight: 600;
}

.milestone-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.milestone-achievement {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid #4caf50;
}

.achievement-icon {
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.achievement-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}

/* 常见问题样式 */
.faq-icon {
  background: linear-gradient(135deg, #ff5722 0%, #ff8a65 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 87, 34, 0.3);
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.faq-card {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-radius: 20rpx;
  overflow: hidden;
  border: 2rpx solid #ffcc02;
}

.question-section {
  background: linear-gradient(135deg, #ffe0b2 0%, #ffcc02 100%);
  padding: 24rpx;
}

.question-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.question-badge {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #ff5722 0%, #ff8a65 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
}

.question-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.answer-section {
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.7);
}

.answer-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.answer-badge {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
}

.answer-content {
  flex: 1;
}

.answer-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

.answer-tips {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #fff9c4 0%, #fff59d 100%);
  border-radius: 16rpx;
  margin-top: 16rpx;
  border: 1rpx solid #fff176;
}

.tips-badge {
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  flex-shrink: 0;
}

.tips-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #e5e7eb;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 16rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.share-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.share-btn:active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.favorite-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.favorite-btn.favorited {
  background: linear-gradient(135deg, #ffe6e6 0%, #ffcccb 100%);
  color: #dc3545;
  border-color: #ff4757;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
}

.btn-icon {
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

.favorite-btn.favorited .btn-icon {
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

